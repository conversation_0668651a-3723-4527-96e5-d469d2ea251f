"""
Pytest-based tests for bond accrual comparison between original and refactored implementations
"""
import pytest
from datetime import date
from decimal import Decimal
from unittest.mock import patch, MagicMock
import pandas as pd

from port.models import (
    Journal, Instrument, Currency, Custodian, Ubo, Partner, Account, Operation, BondAccrual
)
from port.services.provider.bonds_service import BondService


@pytest.fixture
def currencies(db):
    """Create test currencies"""
    usd = Currency.objects.create(currency_code='USD')
    ron = Currency.objects.create(currency_code='RON')
    eur = Currency.objects.create(currency_code='EUR')
    return {'USD': usd, 'RON': ron, 'EUR': eur}


@pytest.fixture
def custodian(db):
    """Create test custodian"""
    return Custodian.objects.create(
        custodian_code='IBKR',
        custodian_name='Interactive Brokers'
    )


@pytest.fixture
def ubo(db):
    """Create test UBO"""
    return Ubo.objects.create(
        ubo_code='TEST_UBO',
        ubo_name='Test UBO'
    )


@pytest.fixture
def partner(db):
    """Create test partner"""
    return Partner.objects.create(
        partner_code='TEST_PARTNER',
        partner_name='Test Partner'
    )


@pytest.fixture
def operations(db):
    """Create test operations"""
    operation_codes = [
        'BOND_BUY_BROKER', 'BOND_SELL_BROKER', 'BOND_COUPON_RECEIVED_BROKER',
        'INTEREST_ACCRUAL_BOND', 'FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS'
    ]
    
    ops = {}
    for code in operation_codes:
        ops[code] = Operation.objects.create(
            operation_code=code,
            operation_name=code.replace('_', ' ').title()
        )
    return ops


@pytest.fixture
def accounts(db, currencies, custodian, ubo):
    """Create test accounts"""
    usd_account = Account.objects.create(
        account_code='IBKR_USD',
        account_name='IBKR USD Account',
        currency=currencies['USD'],
        custodian=custodian,
        ubo=ubo
    )
    
    ron_account = Account.objects.create(
        account_code='IBKR_RON',
        account_name='IBKR RON Account',
        currency=currencies['RON'],
        custodian=custodian,
        ubo=ubo
    )
    
    return {'USD': usd_account, 'RON': ron_account}


@pytest.fixture
def bond_instrument(db, currencies, custodian):
    """Create test bond instrument"""
    return Instrument.objects.create(
        symbol='T 4 3/8 11/30/28',
        instrument_name='US Treasury Bond',
        currency=currencies['USD'],
        custodian=custodian,
        face_value=1000,
        interest=4.375,
        maturity=date(2028, 11, 30),
        bond_issue=date(2018, 11, 30),
        bond_coupon_count=2,
        convention='30/360',
        calendar='TARGET'
    )


@pytest.fixture
def bond_journals(db, ubo, custodian, partner, accounts, bond_instrument, operations):
    """Create test bond journal entries"""
    journals = []
    
    # Bond purchase
    journals.append(Journal.objects.create(
        ubo=ubo,
        custodian=custodian,
        partner=partner,
        account=accounts['USD'],
        instrument=bond_instrument,
        operation=operations['BOND_BUY_BROKER'],
        date=date(2024, 1, 15),
        currency=bond_instrument.currency,
        quantity=10,
        value=95000,
        details='Bond purchase'
    ))
    
    # Bond sale
    journals.append(Journal.objects.create(
        ubo=ubo,
        custodian=custodian,
        partner=partner,
        account=accounts['USD'],
        instrument=bond_instrument,
        operation=operations['BOND_SELL_BROKER'],
        date=date(2024, 6, 15),
        currency=bond_instrument.currency,
        quantity=-5,
        value=48000,
        details='Bond sale'
    ))
    
    # Coupon received
    journals.append(Journal.objects.create(
        ubo=ubo,
        custodian=custodian,
        partner=partner,
        account=accounts['USD'],
        instrument=bond_instrument,
        operation=operations['BOND_COUPON_RECEIVED_BROKER'],
        date=date(2024, 5, 30),
        currency=bond_instrument.currency,
        quantity=0,
        value=2187.5,
        details='Coupon received'
    ))
    
    return journals


@pytest.fixture
def clean_accrual_data(db):
    """Clean accrual data before each test"""
    BondAccrual.objects.all().delete()
    Journal.objects.filter(
        operation__operation_code__in=[
            'INTEREST_ACCRUAL_BOND', 'FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS'
        ]
    ).delete()
    yield
    # Cleanup after test
    BondAccrual.objects.all().delete()
    Journal.objects.filter(
        operation__operation_code__in=[
            'INTEREST_ACCRUAL_BOND', 'FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS'
        ]
    ).delete()


class TestBondAccrualOperations:
    """Test that bond accrual operations are created correctly"""
    
    def test_interest_accrual_operations_created(self, bond_journals, clean_accrual_data):
        """Test that INTEREST_ACCRUAL_BOND operations are created"""
        # Run bond accrual calculation
        results, count, errors = BondService.calculate_bond_accruals()
        
        # Filter for interest accrual operations
        interest_accruals = [
            r for r in results 
            if r.get('operation') == 'INTEREST_ACCRUAL_BOND'
        ]
        
        # Assertions
        assert len(interest_accruals) > 0, "Should create INTEREST_ACCRUAL_BOND operations"
        assert len(errors) == 0, f"Should not have errors: {errors}"
        
        # Verify structure of interest accrual operations
        for accrual in interest_accruals:
            assert accrual.get('operation') == 'INTEREST_ACCRUAL_BOND'
            assert accrual.get('date') is not None, "Should have date"
            assert accrual.get('symbol') is not None, "Should have symbol"
            assert 'accrual' in accrual.get('details', '').lower(), "Details should mention accrual"
    
    def test_fx_difference_operations_created(self, bond_journals, clean_accrual_data):
        """Test that FX_DIF_ACCRUAL operations are created when appropriate"""
        # Run bond accrual calculation
        results, count, errors = BondService.calculate_bond_accruals()
        
        # Look for FX operations
        fx_operations = [
            r for r in results 
            if r.get('operation') in ['FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS']
        ]
        
        # Verify FX operations structure if they exist
        for fx_op in fx_operations:
            assert fx_op.get('operation') in ['FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS']
            assert fx_op.get('currency') == 'RON', "FX operations should be in RON currency"
            assert fx_op.get('details', '').startswith('FxDifAcc'), "FX operations should have 'FxDifAcc' prefix"
            assert fx_op.get('quantity') == 0, "FX operations should have zero quantity"
    
    def test_journal_entries_created(self, bond_journals, clean_accrual_data):
        """Test that journal entries are created for accrual operations"""
        # Run bond accrual calculation
        results, count, errors = BondService.calculate_bond_accruals()
        
        # Check that journal entries were created
        created_journals = Journal.objects.filter(
            operation__operation_code__in=[
                'INTEREST_ACCRUAL_BOND', 'FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS'
            ]
        )
        
        # Assertions
        assert created_journals.count() > 0, "Should create journal entries for accrual operations"
        
        # Verify journal entry structure
        for journal in created_journals:
            assert journal.date is not None, "Journal should have date"
            assert journal.operation is not None, "Journal should have operation"
            assert journal.instrument is not None, "Journal should have instrument"
            assert journal.currency is not None, "Journal should have currency"


class TestDataFlowSteps:
    """Test individual steps in the data flow"""
    
    def test_face_value_multiplication(self, bond_journals, bond_instrument):
        """Test that face value multiplication is correctly applied"""
        # Get bond journals for our test bond
        symbol_bonds = Journal.objects.filter(instrument=bond_instrument)
        
        # Group and check face value multiplication
        grouped = BondService.group_bounds_by_symbols_and_date(symbol_bonds)
        
        assert len(grouped) > 0, "Should have grouped data"
        
        # Check that quantity is multiplied by face value
        for group in grouped:
            quantity = group.get('quantity', 0)
            if quantity != 0:
                # Original quantities: 10, -5, 0
                # With face value 1000: should be 10000, -5000, 0
                assert quantity % 1000 == 0, f"Quantity {quantity} should be multiple of face value 1000"
    
    def test_accrual_date_generation(self, bond_instrument):
        """Test that accrual dates are generated correctly"""
        # Create a sample journal group
        journal_group = [{
            'date': date(2024, 6, 15),
            'symbol': 'T 4 3/8 11/30/28',
            'operation': 'BOND_BUY_BROKER',
            'quantity': 10000,
            'value': 95000
        }]
        
        # Test accrual adjustment
        adjusted = BondService.adjust_accruals(journal_group, bond_instrument)
        
        # Should have added accrual operations
        accrual_ops = [op for op in adjusted if op.get('operation') == 'INTEREST_ACCRUAL_BOND']
        assert len(accrual_ops) > 0, "Should generate accrual operations"
        
        # Check that accrual operations have correct structure
        for accrual in accrual_ops:
            assert accrual.get('operation') == 'INTEREST_ACCRUAL_BOND'
            assert accrual.get('date') is not None, "Accrual should have date"
            assert 'accrual' in accrual.get('details', '').lower(), "Accrual should mention 'accrual' in details"
    
    def test_quantlib_calculations(self, bond_instrument):
        """Test that QuantLib calculations are performed"""
        # Create journal group with accrual operations
        journal_group = [
            {
                'date': date(2024, 6, 15),
                'symbol': 'T 4 3/8 11/30/28',
                'operation': 'BOND_BUY_BROKER',
                'quantity': 10000,
                'value': 95000
            },
            {
                'date': date(2024, 6, 30),
                'symbol': 'T 4 3/8 11/30/28',
                'operation': 'INTEREST_ACCRUAL_BOND',
                'quantity': 0,
                'value': 0,
                'quantity_total': 10000
            }
        ]
        
        # Test accrual calculation
        calculated = BondService.calculate_accruals_for_group_optimized(journal_group, bond_instrument)
        
        # Check that accruals_total is calculated
        accrual_op = next((op for op in calculated if op.get('operation') == 'INTEREST_ACCRUAL_BOND'), None)
        assert accrual_op is not None, "Should have accrual operation"
        
        accruals_total = accrual_op.get('accruals_total')
        assert accruals_total is not None, "Should calculate accruals_total"
        assert isinstance(accruals_total, (int, float)), "Accruals should be numeric"
    
    def test_fx_difference_creation(self):
        """Test that FX difference operations are created"""
        # Create sample data with FX differences
        grouped_bonds = [
            {
                'date': date(2024, 6, 15),
                'symbol': 'T 4 3/8 11/30/28',
                'operation': 'INTEREST_ACCRUAL_BOND',
                'fx_diff': 50.0,  # Positive FX difference
                'currency': 'USD',
                'custodian': 'IBKR',
                'partner': 'TEST_PARTNER',
                'ubo': 'TEST_UBO',
                'details': 'Monthly accrual T 4 3/8 11/30/28'
            },
            {
                'date': date(2024, 7, 15),
                'symbol': 'T 4 3/8 11/30/28',
                'operation': 'INTEREST_ACCRUAL_BOND',
                'fx_diff': -25.0,  # Negative FX difference
                'currency': 'USD',
                'custodian': 'IBKR',
                'partner': 'TEST_PARTNER',
                'ubo': 'TEST_UBO',
                'details': 'Monthly accrual T 4 3/8 11/30/28'
            }
        ]
        
        # Test FX operation creation
        fx_operations = BondService.create_fx_diff_operations(grouped_bonds)
        
        # Should create 2 FX operations
        assert len(fx_operations) == 2, "Should create FX operations for non-zero fx_diff"
        
        # Check first FX operation (positive)
        fx_plus = fx_operations[0]
        assert fx_plus.get('operation') == 'FX_DIF_ACCRUAL_PLUS'
        assert fx_plus.get('currency') == 'RON'
        assert fx_plus.get('value') == 50.0
        assert fx_plus.get('details', '').startswith('FxDifAcc')
        
        # Check second FX operation (negative)
        fx_minus = fx_operations[1]
        assert fx_minus.get('operation') == 'FX_DIF_ACCRUAL_MINUS'
        assert fx_minus.get('currency') == 'RON'
        assert fx_minus.get('value') == 25.0  # Should be absolute value


class TestCalculationAccuracy:
    """Test calculation accuracy and mathematical correctness"""
    
    def test_incremental_accrual_calculations(self):
        """Test that incremental accrual calculations work correctly"""
        # Create sample data with multiple accrual points
        journal_group = [
            {
                'date': date(2024, 6, 15),
                'operation': 'INTEREST_ACCRUAL_BOND',
                'accruals_total': 100.0,
                'accrual_settled': 0.0
            },
            {
                'date': date(2024, 7, 15),
                'operation': 'INTEREST_ACCRUAL_BOND',
                'accruals_total': 200.0,
                'accrual_settled': 0.0
            }
        ]
        
        # Test incremental calculation
        calculated = BondService.compute_incremental_accruals(journal_group)
        
        # Check incremental values
        accrual_ops = [op for op in calculated if op.get('operation') == 'INTEREST_ACCRUAL_BOND']
        
        assert len(accrual_ops) >= 2, "Should have at least 2 accrual operations"
        
        # First accrual should be the full amount (100.0)
        # Second accrual should be the difference (100.0)
        for i, op in enumerate(accrual_ops):
            accrual_incremental = op.get('accrual_incremental')
            assert accrual_incremental is not None, "Should have accrual_incremental"
            
            if i == 0:
                assert accrual_incremental == 100.0, f"First accrual should be 100.0, got {accrual_incremental}"
            elif i == 1:
                assert accrual_incremental == 100.0, f"Second accrual should be 100.0, got {accrual_incremental}"
    
    def test_calculation_pipeline_completeness(self, bond_journals, clean_accrual_data):
        """Test that all calculation steps are executed"""
        # Run bond accrual calculation
        results, count, errors = BondService.calculate_bond_accruals()
        
        # Should have results
        assert len(results) > 0, "Should produce calculation results"
        assert count >= 0, "Count should be non-negative"
        
        # Check that key fields are calculated
        for result in results:
            # Basic fields should be present
            required_fields = ['date', 'symbol', 'operation', 'currency']
            for field in required_fields:
                assert field in result, f"Result should have {field} field"
            
            # Numeric fields should be present and valid
            numeric_fields = ['quantity', 'value', 'quantity_total']
            for field in numeric_fields:
                if field in result:
                    value = result[field]
                    assert isinstance(value, (int, float, Decimal)), f"{field} should be numeric, got {type(value)}"


@pytest.mark.parametrize("operation_type", [
    'INTEREST_ACCRUAL_BOND',
    'FX_DIF_ACCRUAL_PLUS', 
    'FX_DIF_ACCRUAL_MINUS'
])
def test_operation_type_creation(bond_journals, clean_accrual_data, operation_type):
    """Parametrized test for different operation types"""
    # Run bond accrual calculation
    results, count, errors = BondService.calculate_bond_accruals()
    
    # Check if this operation type exists in results
    operations_found = [r.get('operation') for r in results]
    
    if operation_type == 'INTEREST_ACCRUAL_BOND':
        # This should always be created
        assert operation_type in operations_found, f"Should create {operation_type} operations"
    else:
        # FX operations may or may not be created depending on FX differences
        # Just verify structure if they exist
        fx_ops = [r for r in results if r.get('operation') == operation_type]
        for fx_op in fx_ops:
            assert fx_op.get('currency') == 'RON', f"{operation_type} should use RON currency"
            assert fx_op.get('details', '').startswith('FxDifAcc'), f"{operation_type} should have FxDifAcc prefix"


def test_data_consistency_across_operations(bond_journals, clean_accrual_data):
    """Test that data is consistent across different operation types"""
    # Run bond accrual calculation
    results, count, errors = BondService.calculate_bond_accruals()
    
    # Group by symbol
    by_symbol = {}
    for result in results:
        symbol = result.get('symbol')
        if symbol not in by_symbol:
            by_symbol[symbol] = []
        by_symbol[symbol].append(result)
    
    # Check consistency within each symbol
    for symbol, symbol_results in by_symbol.items():
        # All should have same symbol
        for result in symbol_results:
            assert result.get('symbol') == symbol, "Symbol should be consistent within group"
        
        # Should have consistent UBO, custodian, etc.
        if len(symbol_results) > 1:
            first_result = symbol_results[0]
            for result in symbol_results:
                assert result.get('ubo') == first_result.get('ubo'), "UBO should be consistent"
                assert result.get('custodian') == first_result.get('custodian'), "Custodian should be consistent"


def test_bond_accrual_table_population(bond_journals, clean_accrual_data):
    """Test that BondAccrual table is properly populated"""
    # Run bond accrual calculation
    results, count, errors = BondService.calculate_bond_accruals()
    
    # Check that BondAccrual records were created
    bond_accruals = BondAccrual.objects.all()
    assert bond_accruals.count() > 0, "Should create BondAccrual records"
    
    # Verify BondAccrual record structure
    for accrual in bond_accruals:
        assert accrual.date is not None, "BondAccrual should have date"
        assert accrual.instrument is not None, "BondAccrual should have instrument"
        assert accrual.operation is not None, "BondAccrual should have operation"
        
        # Numeric fields should be present
        numeric_fields = ['value', 'accruals_total', 'accrual_incremental']
        for field in numeric_fields:
            if hasattr(accrual, field):
                value = getattr(accrual, field)
                if value is not None:
                    assert isinstance(value, (int, float, Decimal)), f"BondAccrual.{field} should be numeric"


def test_error_handling_robustness(bond_journals, clean_accrual_data):
    """Test that the implementation handles edge cases gracefully"""
    # Run with current data
    results, count, errors = BondService.calculate_bond_accruals()
    
    # Should return valid data structures
    assert isinstance(results, list), "Results should be a list"
    assert isinstance(count, int), "Count should be an integer"
    assert isinstance(errors, list), "Errors should be a list"
    
    # Should not have critical errors that prevent calculation
    assert count >= 0, "Count should be non-negative"
    
    # If there are errors, they should be informative
    for error in errors:
        assert isinstance(error, str), "Errors should be strings"

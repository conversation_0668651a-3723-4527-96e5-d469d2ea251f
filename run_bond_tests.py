#!/usr/bin/env python
"""
Script to run bond accrual pytest tests with different configurations
"""
import subprocess
import sys
import os


def run_command(cmd, description):
    """Run a command and print results"""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {cmd}")
    print('='*60)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.stdout:
            print("STDOUT:")
            print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        print(f"Return code: {result.returncode}")
        return result.returncode == 0
        
    except Exception as e:
        print(f"Error running command: {e}")
        return False


def main():
    """Run various test configurations"""
    print("Bond Accrual Pytest Test Runner")
    print("="*60)
    
    # Change to project directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # Test configurations
    test_configs = [
        {
            'cmd': 'pytest port/tests/test_bond_accruals_pytest.py -v',
            'description': 'Run all bond accrual pytest tests'
        },
        {
            'cmd': 'pytest port/tests/test_bond_integration_pytest.py -v',
            'description': 'Run bond integration tests'
        },
        {
            'cmd': 'pytest -m bond_accruals -v',
            'description': 'Run all tests marked with bond_accruals'
        },
        {
            'cmd': 'pytest -m "bond_accruals and unit" -v',
            'description': 'Run unit tests for bond accruals'
        },
        {
            'cmd': 'pytest -m "bond_accruals and integration" -v',
            'description': 'Run integration tests for bond accruals'
        },
        {
            'cmd': 'pytest -m "bond_accruals and comparison" -v',
            'description': 'Run comparison tests between implementations'
        },
        {
            'cmd': 'pytest -m "bond_accruals and not slow" -v',
            'description': 'Run fast bond accrual tests (excluding slow tests)'
        }
    ]
    
    # Run each configuration
    results = []
    for config in test_configs:
        success = run_command(config['cmd'], config['description'])
        results.append((config['description'], success))
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print('='*60)
    
    for description, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status} - {description}")
    
    # Overall result
    all_passed = all(success for _, success in results)
    if all_passed:
        print(f"\n🎉 All test configurations passed!")
        return 0
    else:
        print(f"\n⚠️  Some test configurations failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())

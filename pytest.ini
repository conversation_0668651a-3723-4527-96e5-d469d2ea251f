[pytest]
DJANGO_SETTINGS_MODULE = nch.settings
python_files = tests.py test_*.py *_tests.py
python_classes = Test*
python_functions = test_*
addopts = -v --reuse-db --nomigrations --tb=short --strict-markers
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    comparison: marks tests that compare implementations
    bond_accruals: marks tests related to bond accrual calculations
testpaths = port/tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::UserWarning
    ignore::django.utils.deprecation.RemovedInDjango50Warning
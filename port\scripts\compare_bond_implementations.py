#!/usr/bin/env python
"""
Script to directly compare outputs between original and refactored bond accrual implementations
"""
import os
import sys
import django
from datetime import date
import pandas as pd
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'port.settings')
django.setup()

from port.models import Journal, BondAccrual
from port.services.provider.bonds_service import BondService


def compare_implementations():
    """Compare the outputs of both implementations"""
    print("=" * 80)
    print("BOND ACCRUAL IMPLEMENTATIONS COMPARISON")
    print("=" * 80)
    
    # Clear existing accrual data
    print("\n1. Clearing existing accrual data...")
    initial_bond_accruals = BondAccrual.objects.count()
    initial_accrual_journals = Journal.objects.filter(
        operation__operation_code__in=[
            'INTEREST_ACCRUAL_BOND', 'FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS'
        ]
    ).count()
    
    print(f"   - Initial BondAccrual records: {initial_bond_accruals}")
    print(f"   - Initial accrual Journal entries: {initial_accrual_journals}")
    
    # Clear accrual data
    BondAccrual.objects.all().delete()
    Journal.objects.filter(
        operation__operation_code__in=[
            'INTEREST_ACCRUAL_BOND', 'FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS'
        ]
    ).delete()
    
    # Get baseline bond data
    print("\n2. Analyzing baseline bond data...")
    bond_journals = Journal.objects.filter(
        operation__operation_code__contains='BOND'
    ).exclude(
        operation__operation_code__in=[
            'INTEREST_ACCRUAL_BOND', 'BOND_ACCRUAL_REVERSAL',
            'FX_DIF_ACCRUAL_MINUS', 'FX_DIF_ACCRUAL_PLUS',
        ]
    )
    
    print(f"   - Bond journal entries found: {bond_journals.count()}")
    
    if bond_journals.count() == 0:
        print("   ⚠️  No bond journal entries found. Cannot perform comparison.")
        return
    
    # Show bond symbols
    symbols = bond_journals.values_list('instrument__symbol', flat=True).distinct()
    print(f"   - Unique bond symbols: {len(symbols)}")
    for symbol in symbols[:5]:  # Show first 5
        print(f"     • {symbol}")
    if len(symbols) > 5:
        print(f"     ... and {len(symbols) - 5} more")
    
    # Run refactored implementation
    print("\n3. Running refactored implementation...")
    try:
        results, count, errors = BondService.calculate_bond_accruals()
        print(f"   ✅ Refactored implementation completed successfully")
        print(f"   - Results count: {len(results)}")
        print(f"   - Processing count: {count}")
        print(f"   - Errors: {len(errors)}")
        
        if errors:
            print("   - Error details:")
            for error in errors[:3]:  # Show first 3 errors
                print(f"     • {error}")
    
    except Exception as e:
        print(f"   ❌ Refactored implementation failed: {e}")
        return
    
    # Analyze results
    print("\n4. Analyzing refactored results...")
    analyze_results(results)
    
    # Check created journal entries
    print("\n5. Checking created journal entries...")
    analyze_journal_entries()
    
    # Check BondAccrual records
    print("\n6. Checking BondAccrual records...")
    analyze_bond_accrual_records()
    
    # Summary comparison
    print("\n7. Summary comparison with expected original behavior...")
    compare_with_expected_original(results)
    
    print("\n" + "=" * 80)
    print("COMPARISON COMPLETE")
    print("=" * 80)


def analyze_results(results):
    """Analyze the results from refactored implementation"""
    if not results:
        print("   ⚠️  No results returned")
        return
    
    # Group by operation type
    by_operation = {}
    for result in results:
        op = result.get('operation', 'UNKNOWN')
        if op not in by_operation:
            by_operation[op] = []
        by_operation[op].append(result)
    
    print(f"   - Total results: {len(results)}")
    print("   - Results by operation type:")
    for op, op_results in by_operation.items():
        print(f"     • {op}: {len(op_results)} entries")
    
    # Check for key operation types
    expected_operations = ['INTEREST_ACCRUAL_BOND', 'FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS']
    for expected_op in expected_operations:
        if expected_op in by_operation:
            print(f"   ✅ {expected_op} operations created")
        else:
            print(f"   ⚠️  {expected_op} operations NOT found")
    
    # Sample result structure
    if results:
        print("   - Sample result structure:")
        sample = results[0]
        for key, value in list(sample.items())[:8]:  # Show first 8 fields
            print(f"     • {key}: {type(value).__name__} = {value}")


def analyze_journal_entries():
    """Analyze created journal entries"""
    accrual_journals = Journal.objects.filter(
        operation__operation_code__in=[
            'INTEREST_ACCRUAL_BOND', 'FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS'
        ]
    ).order_by('date', 'operation__operation_code')
    
    print(f"   - Total accrual journal entries created: {accrual_journals.count()}")
    
    if accrual_journals.count() == 0:
        print("   ⚠️  No accrual journal entries created")
        return
    
    # Group by operation
    by_operation = {}
    for journal in accrual_journals:
        op = journal.operation.operation_code
        if op not in by_operation:
            by_operation[op] = []
        by_operation[op].append(journal)
    
    print("   - Journal entries by operation:")
    for op, journals in by_operation.items():
        print(f"     • {op}: {len(journals)} entries")
        
        # Show date range
        if journals:
            dates = [j.date for j in journals]
            min_date = min(dates)
            max_date = max(dates)
            print(f"       Date range: {min_date} to {max_date}")
            
            # Show value range
            values = [float(j.value) for j in journals if j.value]
            if values:
                print(f"       Value range: {min(values):.2f} to {max(values):.2f}")
    
    # Sample journal entry
    if accrual_journals.exists():
        sample = accrual_journals.first()
        print("   - Sample journal entry:")
        print(f"     • Date: {sample.date}")
        print(f"     • Operation: {sample.operation.operation_code}")
        print(f"     • Instrument: {sample.instrument.symbol}")
        print(f"     • Value: {sample.value}")
        print(f"     • Currency: {sample.currency.currency_code}")


def analyze_bond_accrual_records():
    """Analyze BondAccrual records"""
    bond_accruals = BondAccrual.objects.all().order_by('date', 'operation__operation_code')
    
    print(f"   - Total BondAccrual records created: {bond_accruals.count()}")
    
    if bond_accruals.count() == 0:
        print("   ⚠️  No BondAccrual records created")
        return
    
    # Show key statistics
    if bond_accruals.exists():
        dates = [ba.date for ba in bond_accruals]
        min_date = min(dates)
        max_date = max(dates)
        print(f"   - Date range: {min_date} to {max_date}")
        
        # Show instruments
        instruments = bond_accruals.values_list('instrument__symbol', flat=True).distinct()
        print(f"   - Instruments: {len(instruments)} unique")
        for instrument in instruments[:3]:
            print(f"     • {instrument}")
        
        # Sample record
        sample = bond_accruals.first()
        print("   - Sample BondAccrual record:")
        print(f"     • Date: {sample.date}")
        print(f"     • Instrument: {sample.instrument.symbol}")
        print(f"     • Operation: {sample.operation.operation_code}")
        print(f"     • Accruals Total: {sample.accruals_total}")
        print(f"     • Accrual Incremental: {sample.accrual_incremental}")


def compare_with_expected_original(results):
    """Compare with expected behavior from original implementation"""
    print("   Checking compliance with original implementation patterns:")
    
    # 1. Face value multiplication check
    bond_ops = [r for r in results if r.get('operation') in ['BOND_BUY_BROKER', 'BOND_SELL_BROKER']]
    face_value_ok = True
    for op in bond_ops:
        quantity = op.get('quantity', 0)
        if quantity != 0 and quantity % 1000 != 0:  # Assuming 1000 face value
            face_value_ok = False
            break
    
    if face_value_ok:
        print("   ✅ Face value multiplication appears correct")
    else:
        print("   ⚠️  Face value multiplication may be incorrect")
    
    # 2. Interest accrual operations
    interest_ops = [r for r in results if r.get('operation') == 'INTEREST_ACCRUAL_BOND']
    if interest_ops:
        print(f"   ✅ Interest accrual operations created ({len(interest_ops)})")
        
        # Check for required fields
        required_fields = ['accruals_total', 'accrual_incremental', 'date', 'symbol']
        fields_ok = all(
            all(field in op for field in required_fields)
            for op in interest_ops
        )
        
        if fields_ok:
            print("   ✅ Interest operations have required fields")
        else:
            print("   ⚠️  Some interest operations missing required fields")
    else:
        print("   ❌ No interest accrual operations created")
    
    # 3. FX difference operations
    fx_ops = [r for r in results if r.get('operation') in ['FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS']]
    if fx_ops:
        print(f"   ✅ FX difference operations created ({len(fx_ops)})")
        
        # Check FX operations are in RON
        ron_currency_ok = all(op.get('currency') == 'RON' for op in fx_ops)
        if ron_currency_ok:
            print("   ✅ FX operations correctly use RON currency")
        else:
            print("   ⚠️  Some FX operations not in RON currency")
    else:
        print("   ℹ️  No FX difference operations (may be normal if no FX differences)")
    
    # 4. Data sorting and structure
    dates = [r.get('date') for r in results if r.get('date')]
    if dates:
        sorted_dates = sorted(dates)
        if dates == sorted_dates:
            print("   ✅ Results appear to be properly sorted by date")
        else:
            print("   ⚠️  Results may not be properly sorted")
    
    # 5. Journal entries created
    journal_count = Journal.objects.filter(
        operation__operation_code__in=[
            'INTEREST_ACCRUAL_BOND', 'FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS'
        ]
    ).count()
    
    if journal_count > 0:
        print(f"   ✅ Journal entries created ({journal_count})")
    else:
        print("   ❌ No journal entries created")


if __name__ == "__main__":
    compare_implementations()

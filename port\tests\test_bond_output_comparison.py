"""
Focused tests to compare key outputs between original and refactored bond accrual implementations
"""
import pytest
from datetime import date
from django.test import TestCase
from decimal import Decimal

from port.models import (
    Journal, Instrument, Currency, Custodian, Ubo, Partner, Account, Operation, BondAccrual
)
from port.services.provider.bonds_service import BondService


class BondOutputComparisonTest(TestCase):
    """Test that refactored service produces expected outputs matching original logic"""
    
    @classmethod
    def setUpTestData(cls):
        """Set up minimal test data for comparison"""
        # Create basic entities
        cls.usd = Currency.objects.create(currency_code='USD')
        cls.ron = Currency.objects.create(currency_code='RON')
        
        cls.custodian = Custodian.objects.create(
            custodian_code='IBKR',
            custodian_name='Interactive Brokers'
        )
        
        cls.ubo = Ubo.objects.create(ubo_code='TEST_UBO', ubo_name='Test UBO')
        cls.partner = Partner.objects.create(partner_code='TEST_PARTNER', partner_name='Test Partner')
        
        # Create accounts
        cls.usd_account = Account.objects.create(
            account_code='IBKR_USD',
            account_name='IBKR USD Account',
            currency=cls.usd,
            custodian=cls.custodian,
            ubo=cls.ubo
        )
        
        # Create operations
        operations = [
            'BOND_BUY_BROKER', 'BOND_SELL_BROKER', 'BOND_COUPON_RECEIVED_BROKER',
            'INTEREST_ACCRUAL_BOND', 'FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS'
        ]
        
        cls.operations = {}
        for op_code in operations:
            cls.operations[op_code] = Operation.objects.create(
                operation_code=op_code,
                operation_name=op_code.replace('_', ' ').title()
            )
        
        # Create bond instrument
        cls.bond = Instrument.objects.create(
            symbol='T 4 3/8 11/30/28',
            instrument_name='US Treasury Bond',
            currency=cls.usd,
            custodian=cls.custodian,
            face_value=1000,
            interest=4.375,
            maturity=date(2028, 11, 30),
            bond_issue=date(2018, 11, 30),
            bond_coupon_count=2,
            convention='30/360',
            calendar='TARGET'
        )
        
        # Create test journal entries
        cls.create_test_journals()

    @classmethod
    def create_test_journals(cls):
        """Create test journal entries"""
        test_data = [
            {
                'operation': cls.operations['BOND_BUY_BROKER'],
                'date': date(2024, 1, 15),
                'quantity': 10,
                'value': 95000,
                'details': 'Bond purchase'
            },
            {
                'operation': cls.operations['BOND_SELL_BROKER'],
                'date': date(2024, 6, 15),
                'quantity': -5,
                'value': 48000,
                'details': 'Bond sale'
            },
            {
                'operation': cls.operations['BOND_COUPON_RECEIVED_BROKER'],
                'date': date(2024, 5, 30),
                'quantity': 0,
                'value': 2187.5,  # Coupon payment
                'details': 'Coupon received'
            }
        ]
        
        for data in test_data:
            Journal.objects.create(
                ubo=cls.ubo,
                custodian=cls.custodian,
                partner=cls.partner,
                account=cls.usd_account,
                instrument=cls.bond,
                operation=data['operation'],
                date=data['date'],
                currency=cls.usd,
                quantity=data['quantity'],
                value=data['value'],
                details=data['details']
            )

    def setUp(self):
        """Clear accrual data before each test"""
        BondAccrual.objects.all().delete()
        Journal.objects.filter(
            operation__operation_code__in=[
                'INTEREST_ACCRUAL_BOND', 'FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS'
            ]
        ).delete()

    def test_interest_accrual_operations_created(self):
        """Test that INTEREST_ACCRUAL_BOND operations are created"""
        # Run bond accrual calculation
        results, _, _ = BondService.calculate_bond_accruals()
        
        # Filter for interest accrual operations
        interest_accruals = [
            r for r in results 
            if r.get('operation') == 'INTEREST_ACCRUAL_BOND'
        ]
        
        # Should have interest accrual operations
        self.assertGreater(len(interest_accruals), 0, 
                          "Should create INTEREST_ACCRUAL_BOND operations")
        
        # Verify structure of interest accrual operations
        for accrual in interest_accruals:
            self.assertEqual(accrual.get('operation'), 'INTEREST_ACCRUAL_BOND')
            self.assertIsNotNone(accrual.get('date'), "Should have date")
            self.assertIsNotNone(accrual.get('symbol'), "Should have symbol")
            self.assertIn('accrual', accrual.get('details', '').lower(), 
                         "Details should mention accrual")

    def test_fx_difference_operations_created(self):
        """Test that FX_DIF_ACCRUAL operations are created when appropriate"""
        # Run bond accrual calculation
        results, _, _ = BondService.calculate_bond_accruals()
        
        # Look for FX operations
        fx_operations = [
            r for r in results 
            if r.get('operation') in ['FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS']
        ]
        
        # Verify FX operations structure if they exist
        for fx_op in fx_operations:
            self.assertIn(fx_op.get('operation'), ['FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS'])
            self.assertEqual(fx_op.get('currency'), 'RON', 
                           "FX operations should be in RON currency")
            self.assertTrue(fx_op.get('details', '').startswith('FxDifAcc'), 
                          "FX operations should have 'FxDifAcc' prefix")
            self.assertEqual(fx_op.get('quantity'), 0, 
                           "FX operations should have zero quantity")

    def test_journal_entries_created(self):
        """Test that journal entries are created for accrual operations"""
        # Run bond accrual calculation
        results, _, _ = BondService.calculate_bond_accruals()
        
        # Check that journal entries were created
        created_journals = Journal.objects.filter(
            operation__operation_code__in=[
                'INTEREST_ACCRUAL_BOND', 'FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS'
            ]
        )
        
        # Should have created journal entries
        self.assertGreater(created_journals.count(), 0, 
                          "Should create journal entries for accrual operations")
        
        # Verify journal entry structure
        for journal in created_journals:
            self.assertIsNotNone(journal.date, "Journal should have date")
            self.assertIsNotNone(journal.operation, "Journal should have operation")
            self.assertIsNotNone(journal.instrument, "Journal should have instrument")
            self.assertIsNotNone(journal.currency, "Journal should have currency")

    def test_face_value_multiplication_applied(self):
        """Test that face value multiplication is correctly applied"""
        # Run bond accrual calculation
        results, _, _ = BondService.calculate_bond_accruals()
        
        # Check that quantities reflect face value multiplication
        # Original quantities: 10, -5, 0
        # With face value 1000: should be 10000, -5000, 0
        
        bond_operations = [
            r for r in results 
            if r.get('operation') in ['BOND_BUY_BROKER', 'BOND_SELL_BROKER']
        ]
        
        for op in bond_operations:
            quantity = op.get('quantity', 0)
            # Should be in multiples of face value (1000)
            if quantity != 0:
                self.assertEqual(quantity % 1000, 0, 
                               f"Quantity {quantity} should be multiple of face value 1000")

    def test_calculation_pipeline_completeness(self):
        """Test that all calculation steps are executed"""
        # Run bond accrual calculation
        results, _, _ = BondService.calculate_bond_accruals()
        
        # Should have results
        self.assertGreater(len(results), 0, "Should produce calculation results")
        
        # Check that key fields are calculated
        for result in results:
            # Basic fields should be present
            required_fields = ['date', 'symbol', 'operation', 'currency']
            for field in required_fields:
                self.assertIn(field, result, f"Result should have {field} field")
            
            # Numeric fields should be present and valid
            numeric_fields = ['quantity', 'value', 'quantity_total']
            for field in numeric_fields:
                if field in result:
                    value = result[field]
                    self.assertIsInstance(value, (int, float, Decimal), 
                                        f"{field} should be numeric, got {type(value)}")

    def test_data_consistency_across_operations(self):
        """Test that data is consistent across different operation types"""
        # Run bond accrual calculation
        results, _, _ = BondService.calculate_bond_accruals()
        
        # Group by symbol
        by_symbol = {}
        for result in results:
            symbol = result.get('symbol')
            if symbol not in by_symbol:
                by_symbol[symbol] = []
            by_symbol[symbol].append(result)
        
        # Check consistency within each symbol
        for symbol, symbol_results in by_symbol.items():
            # All should have same symbol
            for result in symbol_results:
                self.assertEqual(result.get('symbol'), symbol, 
                               "Symbol should be consistent within group")
            
            # Should have consistent UBO, custodian, etc.
            first_result = symbol_results[0]
            for result in symbol_results:
                self.assertEqual(result.get('ubo'), first_result.get('ubo'),
                               "UBO should be consistent")
                self.assertEqual(result.get('custodian'), first_result.get('custodian'),
                               "Custodian should be consistent")

    def test_original_vs_refactored_key_outputs(self):
        """Test that key outputs match expected patterns from original implementation"""
        # Run refactored implementation
        results, _, _ = BondService.calculate_bond_accruals()
        
        # Expected patterns based on original implementation:
        
        # 1. Should have INTEREST_ACCRUAL_BOND operations
        interest_ops = [r for r in results if r.get('operation') == 'INTEREST_ACCRUAL_BOND']
        self.assertGreater(len(interest_ops), 0, "Should have interest accrual operations")
        
        # 2. Interest operations should have accrual calculations
        for op in interest_ops:
            self.assertIn('accruals_total', op, "Should have accruals_total")
            self.assertIn('accrual_incremental', op, "Should have accrual_incremental")
        
        # 3. Should have proper date ordering
        all_dates = [r.get('date') for r in results if r.get('date')]
        sorted_dates = sorted(all_dates)
        # Results should be in chronological order (or at least have dates)
        self.assertEqual(len(all_dates), len(sorted_dates), "All results should have dates")
        
        # 4. Should have proper operation priorities
        for result in results:
            priority = result.get('priority')
            if priority is not None:
                self.assertIsInstance(priority, int, "Priority should be integer")
                self.assertGreaterEqual(priority, 1, "Priority should be >= 1")

    def test_error_handling_and_robustness(self):
        """Test that the implementation handles edge cases gracefully"""
        # This test ensures the refactored code doesn't crash on edge cases
        
        # Run with current data
        results, count, errors = BondService.calculate_bond_accruals()
        
        # Should return valid data structures
        self.assertIsInstance(results, list, "Results should be a list")
        self.assertIsInstance(count, int, "Count should be an integer")
        self.assertIsInstance(errors, list, "Errors should be a list")
        
        # Should not have critical errors that prevent calculation
        self.assertGreaterEqual(count, 0, "Count should be non-negative")
        
        # If there are errors, they should be informative
        for error in errors:
            self.assertIsInstance(error, str, "Errors should be strings")

    def test_bond_accrual_table_population(self):
        """Test that BondAccrual table is properly populated"""
        # Run bond accrual calculation
        results, _, _ = BondService.calculate_bond_accruals()
        
        # Check that BondAccrual records were created
        bond_accruals = BondAccrual.objects.all()
        self.assertGreater(bond_accruals.count(), 0, 
                          "Should create BondAccrual records")
        
        # Verify BondAccrual record structure
        for accrual in bond_accruals:
            self.assertIsNotNone(accrual.date, "BondAccrual should have date")
            self.assertIsNotNone(accrual.instrument, "BondAccrual should have instrument")
            self.assertIsNotNone(accrual.operation, "BondAccrual should have operation")
            
            # Numeric fields should be present
            numeric_fields = ['value', 'accruals_total', 'accrual_incremental']
            for field in numeric_fields:
                if hasattr(accrual, field):
                    value = getattr(accrual, field)
                    if value is not None:
                        self.assertIsInstance(value, (int, float, Decimal),
                                            f"BondAccrual.{field} should be numeric")

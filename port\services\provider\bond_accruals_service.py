import logging
import QuantLib as ql
from typing import Dict, List, Tuple, Optional
from collections import defaultdict
from datetime import date, datetime
from dateutil.relativedelta import relativedelta

from django.db import transaction
from django.db.models import QuerySet, Sum, F
from django.conf import settings

from port.models import (
    Journal, Instrument, Bnr, Cur<PERSON>cy, <PERSON><PERSON><PERSON>ian, Partner, Account, 
    Operation, Ubo, Accounting
)

logger = logging.getLogger(__name__)


class BondAccrualsService:
    """
    Service for calculating bond accruals using Django ORM instead of pandas.
    Refactored from port/management/commands/bond_accruals_ql.py and bondlib.py
    """
    
    PAYMENT_CONVENTION = {
        '30/360': ql.Thirty360(ql.Thirty360.ISDA),
        'ISMA-30/360': ql.Thirty360(ql.Thirty360.European),
        'ACT/ACT': ql.ActualActual(ql.ActualActual.ISMA),
    }

    CALENDAR_MAP = {
        'GovernmentBond': ql.UnitedStates(ql.UnitedStates.GovernmentBond),
        'TARGET': ql.TARGET(),
        'NYSE': ql.UnitedStates(ql.UnitedStates.NYSE),
        'FederalReserve': ql.UnitedStates(ql.UnitedStates.FederalReserve),
        'Settlement': ql.UnitedStates(ql.UnitedStates.Settlement),
    }

    def __init__(self):
        """Initialize the bond accruals service"""
        self.day_counter = ql.Actual360()
        
    @classmethod
    def calculate_bond_accruals(cls) -> Tuple[List[Dict], int, List[str]]:
        """
        Main method to calculate bond accruals and create journal entries.
        
        Returns:
            Tuple of (final_results, count, errors)
        """
        logger.info("Starting bond accruals calculation with Django ORM...")
        
        service = cls()
        
        try:
            # Get data using Django ORM
            bond_operations, bond_instruments, bnr_rates, bnr_eom_rates = service.query_data()
            
            if not bond_operations.exists() or not bond_instruments.exists():
                logger.info("No bond operations or instruments found")
                return [], 0, []
            
            final_results = []
            errors = []
            count = 0
            
            # Process each bond symbol
            for instrument in bond_instruments:
                try:
                    logger.info(f"Processing bond: {instrument.symbol}")
                    
                    # Get operations for this symbol
                    symbol_operations = bond_operations.filter(instrument__symbol=instrument.symbol)
                    
                    if not symbol_operations.exists():
                        logger.error(f"No operations found for {instrument.symbol}")
                        continue
                    
                    # Process this bond symbol
                    bond_results = service.process_bond_symbol(
                        symbol_operations, instrument, bnr_rates, bnr_eom_rates
                    )
                    
                    if bond_results:
                        final_results.extend(bond_results)
                        count += len(bond_results)
                        
                        # Create journal entries automatically
                        service.create_journal_entries(bond_results, instrument)
                        
                except Exception as e:
                    logger.error(f"Error processing bond {instrument.symbol}: {str(e)}", exc_info=True)
                    errors.append(instrument.symbol)
            
            logger.info(f"Completed bond accruals calculation: {count} entries, {len(errors)} errors")
            return final_results, count, errors
            
        except Exception as e:
            logger.error(f"Error in bond accruals calculation: {str(e)}", exc_info=True)
            return [], 0, [str(e)]

    def query_data(self) -> Tuple[QuerySet, QuerySet, QuerySet, QuerySet]:
        """
        Query data using Django ORM instead of pandas.
        
        Returns:
            Tuple of (bond_operations, bond_instruments, bnr_rates, bnr_eom_rates)
        """
        # Query bond operations (excluding previous accruals)
        removed_ops = [
            'INTEREST_ACCRUAL_BOND', 'BOND_ACCRUAL_REVERSAL',
            'FX_DIF_ACCRUAL_MINUS', 'FX_DIF_ACCRUAL_PLUS',
        ]
        
        bond_operations = Journal.objects.filter(
            operation__operation_code__contains='BOND'
        ).exclude(
            operation__operation_code__in=removed_ops
        ).select_related(
            'operation', 'instrument', 'instrument__currency',
            'ubo', 'custodian', 'partner', 'account'
        ).order_by('instrument__symbol', 'date')
        
        # Query bond instruments
        bond_instruments = Instrument.objects.filter(
            type='BOND'
        ).select_related(
            'currency', 'custodian'
        )
        
        # Query BNR rates
        bnr_rates = Bnr.objects.filter(
            currency_code__currency_code__in=['EUR', 'USD', 'MXN']
        ).exclude(
            value_exact__isnull=True
        ).select_related('currency_code').order_by('date', 'currency_code')
        
        # Query BNR end-of-month rates
        bnr_eom_rates = self.get_bnr_eom_rates()
        
        logger.info(f"Found {bond_operations.count()} bond operations")
        logger.info(f"Found {bond_instruments.count()} bond instruments")
        logger.info(f"Found {bnr_rates.count()} BNR rates")
        
        return bond_operations, bond_instruments, bnr_rates, bnr_eom_rates

    def get_bnr_eom_rates(self) -> QuerySet:
        """Get end-of-month BNR rates using Django ORM"""
        from django.db.models import Max, Q

        # Get the latest BNR rate for each currency and month
        bnr_eom = Bnr.objects.filter(
            currency_code__currency_code__in=['EUR', 'USD', 'MXN'],
            value_exact__isnull=False
        ).extra(
            select={'month': "DATE_TRUNC('month', date)"}
        ).values(
            'currency_code', 'month'
        ).annotate(
            latest_date=Max('date')
        ).values_list(
            'currency_code', 'latest_date'
        )

        # Get the actual BNR records for those dates
        if not bnr_eom:
            return Bnr.objects.none()

        # Build Q objects for filtering
        q_objects = []
        for currency_id, latest_date in bnr_eom:
            q_objects.append(
                Q(currency_code=currency_id) & Q(date=latest_date)
            )

        if q_objects:
            # Combine all Q objects with OR
            combined_q = q_objects[0]
            for q_obj in q_objects[1:]:
                combined_q |= q_obj

            return Bnr.objects.filter(combined_q).select_related('currency_code')

        return Bnr.objects.none()

    def process_bond_symbol(self, operations: QuerySet, instrument: Instrument, 
                          bnr_rates: QuerySet, bnr_eom_rates: QuerySet) -> List[Dict]:
        """
        Process operations for a single bond symbol.
        
        Args:
            operations: QuerySet of Journal operations for this symbol
            instrument: Instrument object for this bond
            bnr_rates: QuerySet of BNR rates
            bnr_eom_rates: QuerySet of end-of-month BNR rates
            
        Returns:
            List of processed bond accrual records
        """
        # Convert operations to list of dictionaries for processing
        operations_data = self.convert_operations_to_dict(operations, instrument)
        
        if not operations_data:
            return []
        
        # Get coupon schedule
        coupon_dates = self.get_coupon_schedule(instrument)
        
        # Correct coupon received dates
        operations_data = self.correct_coupon_dates(operations_data, coupon_dates)
        
        # Set priorities
        operations_data = self.set_priorities(operations_data)
        
        # Add accrual dates and calculate quantities
        operations_data = self.add_accrual_dates(operations_data, instrument)
        
        # Calculate accruals using QuantLib
        operations_data = self.calculate_accruals(operations_data, instrument)
        
        # Add BNR rates
        operations_data = self.add_bnr_rates(operations_data, bnr_rates, bnr_eom_rates)
        
        # Calculate incremental accruals and FX differences
        operations_data = self.calculate_incremental_accruals(operations_data)
        operations_data = self.calculate_fx_differences(operations_data)
        
        return operations_data

    def convert_operations_to_dict(self, operations: QuerySet, instrument: Instrument) -> List[Dict]:
        """Convert Django QuerySet to list of dictionaries for processing"""
        operations_list = []
        
        # Group operations by date and operation, summing values and quantities
        grouped_ops = operations.values(
            'ubo__ubo_code', 'custodian__custodian_code', 'partner__partner_code',
            'date', 'operation__operation_code', 'account__account_code'
        ).annotate(
            total_value=Sum('value'),
            total_quantity=Sum(F('quantity') * instrument.face_value),
            details_list=Sum('details')  # This will need custom aggregation
        ).order_by('date')
        
        for op in grouped_ops:
            operations_list.append({
                'ubo': op['ubo__ubo_code'],
                'custodian': op['custodian__custodian_code'],
                'partner': op['partner__partner_code'],
                'date': op['date'],
                'symbol': instrument.symbol,
                'currency': instrument.currency.currency_code,
                'operation': op['operation__operation_code'],
                'account': op['account__account_code'],
                'instrument': f"{op['custodian__custodian_code']}_{instrument.symbol}",
                'value': float(op['total_value']),
                'quantity': float(op['total_quantity']),
                'details': f"Bond operation {instrument.symbol}",
                'priority': 1
            })
        
        return operations_list

    def get_coupon_schedule(self, instrument: Instrument) -> List[date]:
        """Get coupon schedule for a bond using QuantLib"""
        calendar = self.CALENDAR_MAP.get(instrument.calendar, self.CALENDAR_MAP['TARGET'])

        frequency = ql.Annual
        if instrument.bond_coupon_count == 2:
            frequency = ql.Semiannual

        issue_date = ql.Date(
            instrument.bond_issue.day,
            instrument.bond_issue.month,
            instrument.bond_issue.year
        )
        maturity_date = ql.Date(
            instrument.maturity.day,
            instrument.maturity.month,
            instrument.maturity.year
        )
        first_coupon = ql.Date(
            instrument.bond_first_coupon.day,
            instrument.bond_first_coupon.month,
            instrument.bond_first_coupon.year
        )

        schedule_coupon = ql.Schedule(
            issue_date,
            maturity_date,
            ql.Period(frequency),
            calendar,
            ql.Unadjusted,
            ql.Unadjusted,
            ql.DateGeneration.Backward,
            True,
        )

        # Convert to Python dates and filter for dates >= first_coupon
        coupon_dates = []
        for ql_date in schedule_coupon:
            if ql_date >= first_coupon:
                py_date = date(ql_date.year(), ql_date.month(), ql_date.dayOfMonth())
                coupon_dates.append(py_date)

        return coupon_dates

    def correct_coupon_dates(self, operations_data: List[Dict], coupon_dates: List[date]) -> List[Dict]:
        """Correct BOND_COUPON_RECEIVED_BROKER dates to match actual coupon dates"""
        for operation in operations_data:
            if operation['operation'] == 'BOND_COUPON_RECEIVED_BROKER':
                original_date = operation['date']

                # Find closest coupon date within 5 days
                valid_coupon_dates = [
                    cd for cd in coupon_dates
                    if cd <= original_date and (original_date - cd).days <= 5
                ]

                if valid_coupon_dates:
                    closest_coupon_date = max(valid_coupon_dates)
                    operation['date'] = closest_coupon_date
                    logger.debug(f"Corrected coupon date from {original_date} to {closest_coupon_date}")

        return operations_data

    def set_priorities(self, operations_data: List[Dict]) -> List[Dict]:
        """Set operation priorities for sorting"""
        for operation in operations_data:
            if operation['operation'] == 'BOND_COUPON_RECEIVED_BROKER':
                operation['priority'] = 10
            else:
                operation['priority'] = 1
        return operations_data

    def add_accrual_dates(self, operations_data: List[Dict], instrument: Instrument) -> List[Dict]:
        """Add monthly and interim accrual dates"""
        if not operations_data:
            return operations_data

        # Find date range
        start_date = min(op['date'] for op in operations_data)
        end_date = min(datetime.today().date(), instrument.maturity)

        # Generate monthly accrual dates (end of month)
        accrual_days = []
        current_date = start_date.replace(day=1)
        while current_date <= end_date:
            eom_date = (current_date + relativedelta(months=1) - relativedelta(days=1))
            if eom_date <= end_date:
                accrual_days.append(eom_date)
            current_date += relativedelta(months=1)

        # Get other operation dates for interim accruals
        operation_dates = set(op['date'] for op in operations_data)
        other_accrual_dates = sorted(operation_dates - set(accrual_days))

        # Add monthly accruals
        for accrual_date in accrual_days:
            operations_data.append({
                'operation': 'INTEREST_ACCRUAL_BOND',
                'date': accrual_date,
                'details': f'Monthly accrual {instrument.symbol}',
                'quantity': 0,
                'priority': 4,
                'symbol': instrument.symbol,
                'currency': instrument.currency.currency_code,
                'value': 0,
            })

        # Add interim accruals
        for accrual_date in other_accrual_dates:
            operations_data.append({
                'operation': 'INTEREST_ACCRUAL_BOND',
                'date': accrual_date,
                'details': f'Interm. accrual trade date {instrument.symbol}',
                'quantity': 0,
                'priority': 3,
                'symbol': instrument.symbol,
                'currency': instrument.currency.currency_code,
                'value': 0,
            })

        # Sort by date and priority
        operations_data.sort(key=lambda x: (x['date'], x.get('priority', 0)))

        # Forward fill missing values
        last_values = {}
        for operation in operations_data:
            for key in ['ubo', 'custodian', 'partner', 'account', 'instrument']:
                if key in operation and operation[key] is not None:
                    last_values[key] = operation[key]
                elif key in last_values:
                    operation[key] = last_values[key]

        # Calculate cumulative quantities
        quantity_total = 0
        for operation in operations_data:
            quantity_total += operation.get('quantity', 0)
            operation['quantity_total'] = quantity_total

        return operations_data

    def calculate_accruals(self, operations_data: List[Dict], instrument: Instrument) -> List[Dict]:
        """Calculate accrued interest using QuantLib"""
        calendar = self.CALENDAR_MAP.get(instrument.calendar, self.CALENDAR_MAP['TARGET'])
        payment_convention = self.PAYMENT_CONVENTION.get(
            instrument.convention, self.PAYMENT_CONVENTION['ACT/ACT']
        )

        # Create coupon schedule
        schedule_coupon = self.get_coupon_schedule_ql(instrument)
        coupon_dates = list(schedule_coupon.dates())

        for operation in operations_data:
            trade_date = ql.Date(
                operation['date'].day,
                operation['date'].month,
                operation['date'].year
            )

            settlement_days = 2

            # Adjust settlement days for USD after May 28, 2024
            new_settlement_cutoff_date = ql.Date(28, 5, 2024)
            if (instrument.currency.currency_code == 'USD') and (trade_date > new_settlement_cutoff_date):
                settlement_days = 1

            # Ad-hoc correction for Settlement calendar
            if instrument.calendar == 'Settlement':
                settlement_days = 1

            target_settlement_date = calendar.advance(
                trade_date,
                ql.Period(settlement_days, ql.Days),
                ql.Following
            )

            # If target settlement date is a coupon date, use trade date as settlement
            if target_settlement_date in coupon_dates:
                settlement_date = trade_date
            else:
                settlement_date = target_settlement_date

            # Set evaluation date
            ql.Settings.instance().evaluationDate = settlement_date

            # Create bond
            coupon_rate = instrument.interest / 100.0
            bond = ql.FixedRateBond(
                settlement_days,
                100,  # Face amount
                schedule_coupon,
                [coupon_rate],
                payment_convention
            )

            # Calculate accrued interest
            accrued_percentage = bond.accruedAmount(settlement_date)
            operation['accruals_total'] = (accrued_percentage / 100.0) * operation['quantity_total']

        return operations_data

    def get_coupon_schedule_ql(self, instrument: Instrument) -> ql.Schedule:
        """Get QuantLib coupon schedule"""
        calendar = self.CALENDAR_MAP.get(instrument.calendar, self.CALENDAR_MAP['TARGET'])

        frequency = ql.Annual
        if instrument.bond_coupon_count == 2:
            frequency = ql.Semiannual

        issue_date = ql.Date(
            instrument.bond_issue.day,
            instrument.bond_issue.month,
            instrument.bond_issue.year
        )
        maturity_date = ql.Date(
            instrument.maturity.day,
            instrument.maturity.month,
            instrument.maturity.year
        )

        return ql.Schedule(
            issue_date,
            maturity_date,
            ql.Period(frequency),
            calendar,
            ql.Unadjusted,
            ql.Unadjusted,
            ql.DateGeneration.Backward,
            True,
        )

    def add_bnr_rates(self, operations_data: List[Dict], bnr_rates: QuerySet,
                     bnr_eom_rates: QuerySet) -> List[Dict]:
        """Add BNR rates to operations data"""
        # Build BNR rate dictionaries for efficient lookup
        bnr_dict = defaultdict(list)
        for bnr in bnr_rates:
            currency_code = bnr.currency_code.currency_code
            bnr_dict[currency_code].append((bnr.date, bnr.value_exact))

        # Sort by date for each currency
        for currency in bnr_dict:
            bnr_dict[currency].sort()

        bnr_eom_dict = defaultdict(list)
        for bnr in bnr_eom_rates:
            currency_code = bnr.currency_code.currency_code
            bnr_eom_dict[currency_code].append((bnr.date, bnr.value_exact))

        # Sort by date for each currency
        for currency in bnr_eom_dict:
            bnr_eom_dict[currency].sort()

        # Add rates to operations
        for operation in operations_data:
            currency = operation.get('currency', 'RON')
            op_date = operation['date']

            # Get regular BNR rate (latest before or on date, excluding exact match)
            operation['bnr'] = self.get_latest_rate(bnr_dict.get(currency, []), op_date, allow_exact=False)

            # Get end-of-month BNR rate (latest before or on date, including exact match)
            operation['bnr_eom'] = self.get_latest_rate(bnr_eom_dict.get(currency, []), op_date, allow_exact=True)

        return operations_data

    def get_latest_rate(self, rate_list: List[Tuple[date, float]], target_date: date,
                       allow_exact: bool = True) -> float:
        """Get the latest rate before or on the target date"""
        if not rate_list:
            return 1.0  # Default rate for RON or missing data

        # Find the latest rate before or on the target date
        latest_rate = 1.0
        for rate_date, rate_value in rate_list:
            if allow_exact:
                if rate_date <= target_date:
                    latest_rate = rate_value
                else:
                    break
            else:
                if rate_date < target_date:
                    latest_rate = rate_value
                elif rate_date == target_date:
                    continue  # Skip exact matches when not allowed
                else:
                    break

        return latest_rate

    def calculate_incremental_accruals(self, operations_data: List[Dict]) -> List[Dict]:
        """Calculate incremental accruals and settled amounts"""
        # Map accrual settled amounts
        operations_data = self.map_accrual_settled(operations_data)

        # Apply coupon settled amounts
        operations_data = self.apply_coupon_settled(operations_data)

        # Calculate incremental accruals
        accrual_operations = [op for op in operations_data
                            if op['operation'] in ['INTEREST_ACCRUAL_BOND', 'BOND_COUPON_RECEIVED_BROKER']]
        accrual_operations.sort(key=lambda x: (x['date'], x.get('priority', 0)))

        prev_total = 0
        for operation in accrual_operations:
            if operation['operation'] == 'INTEREST_ACCRUAL_BOND':
                accrual_total = operation.get('accruals_total', 0)
                accrual_settled = operation.get('accrual_settled', 0)
                incremental = accrual_total - prev_total + accrual_settled
                operation['accrual_incremental'] = incremental
                operation['value'] = incremental
                prev_total = accrual_total

        return operations_data

    def map_accrual_settled(self, operations_data: List[Dict]) -> List[Dict]:
        """Map settled interest amounts to accrual dates"""
        ops_settled = ['BOND_INTEREST_PAID_BROKER', 'BOND_INTEREST_RECEIVED_BROKER']

        # Sum settled amounts by date
        settled_by_date = defaultdict(float)
        for operation in operations_data:
            if operation['operation'] in ops_settled:
                settled_by_date[operation['date']] += operation.get('value', 0)

        # Apply to accrual operations
        for operation in operations_data:
            if operation['operation'] == 'INTEREST_ACCRUAL_BOND':
                operation['accrual_settled'] = settled_by_date.get(operation['date'], 0)
            else:
                operation['accrual_settled'] = 0

        return operations_data

    def apply_coupon_settled(self, operations_data: List[Dict]) -> List[Dict]:
        """Apply coupon payments to accrual totals"""
        # Map coupon payments by date
        coupon_payments = {}
        for operation in operations_data:
            if operation['operation'] == 'BOND_COUPON_RECEIVED_BROKER':
                coupon_payments[operation['date']] = operation.get('value', 0)

        # Apply to accrual operations
        for operation in operations_data:
            if operation['operation'] == 'INTEREST_ACCRUAL_BOND':
                coupon_settled = coupon_payments.get(operation['date'], 0)
                operation['coupon_settled'] = coupon_settled
                if coupon_settled > 0:
                    operation['accruals_total'] = coupon_settled
            else:
                operation['coupon_settled'] = 0

        return operations_data

    def calculate_fx_differences(self, operations_data: List[Dict]) -> List[Dict]:
        """Calculate FX differences and create FX operations"""
        # Filter for accrual and coupon operations
        fx_operations = [op for op in operations_data
                        if op['operation'] in ['INTEREST_ACCRUAL_BOND', 'BOND_COUPON_RECEIVED_BROKER']]
        fx_operations.sort(key=lambda x: (x['date'], x.get('priority', 0)))

        # Determine revaluation dates (end of month)
        fx_reev_days = []
        if fx_operations:
            start_date = min(op['date'] for op in fx_operations)
            end_date = max(op['date'] for op in fx_operations)
            current_date = start_date.replace(day=1)
            while current_date <= end_date:
                eom_date = (current_date + relativedelta(months=1) - relativedelta(days=1))
                if eom_date <= end_date:
                    fx_reev_days.append(eom_date)
                current_date += relativedelta(months=1)

        # Mark revaluation dates
        for operation in fx_operations:
            operation['revalue'] = operation['date'] in fx_reev_days

        # Calculate accrual differences and FX
        prev_total = 0
        total_accrual_valuta = 0
        total_accrual_ron = 0
        prev_total_fx = 0

        for operation in fx_operations:
            # Calculate accrual difference
            accrual_total = operation.get('accruals_total', 0)
            accrual = accrual_total - prev_total
            prev_total = accrual_total

            # Reverse accruals at coupon payment
            if operation['operation'] == 'BOND_COUPON_RECEIVED_BROKER':
                accrual = 0
                accrual_valuta = -total_accrual_valuta
            else:
                accrual_valuta = accrual
                total_accrual_valuta += accrual_valuta

            # Calculate RON amounts
            bnr = operation.get('bnr', 1.0)
            accrual_ron = round(accrual_valuta * bnr, 2)
            total_accrual_ron += accrual_ron

            # Revalue total amounts
            bnr_eom = operation.get('bnr_eom', bnr)
            revalue_rate = bnr_eom if operation.get('revalue') else bnr
            revalue_total = round(total_accrual_valuta * revalue_rate, 2)

            # Calculate FX difference
            total_fx_diff = revalue_total - total_accrual_ron
            fx_diff = round(total_fx_diff - prev_total_fx, 2)
            prev_total_fx = total_fx_diff

            # Update operation with calculated values
            operation.update({
                'accrual': accrual,
                'accrual_valuta': accrual_valuta,
                'accrual_ron': accrual_ron,
                'total_accrual_valuta': total_accrual_valuta,
                'total_accrual_ron': total_accrual_ron,
                'revalue_total': revalue_total,
                'total_fx_diff': total_fx_diff,
                'fx_diff': fx_diff,
            })

        # Create FX difference operations
        fx_diff_operations = []
        for operation in fx_operations:
            fx_diff = operation.get('fx_diff', 0)
            if fx_diff != 0:
                fx_operation_code = 'FX_DIF_ACCRUAL_PLUS' if fx_diff > 0 else 'FX_DIF_ACCRUAL_MINUS'

                fx_operation = {
                    'ubo': operation.get('ubo'),
                    'custodian': operation.get('custodian'),
                    'partner': operation.get('partner'),
                    'date': operation['date'],
                    'symbol': operation['symbol'],
                    'currency': 'RON',
                    'operation': fx_operation_code,
                    'account': f"{operation.get('custodian')}_RON",
                    'instrument': f"{operation.get('custodian')}_{operation['symbol']}",
                    'value': abs(fx_diff),
                    'value_ron': abs(fx_diff),
                    'quantity': 0,
                    'details': f"FxDifAcc {operation.get('details', '')}",
                    'priority': 5,
                    'bnr': 1.0,
                    'fx_diff': 0,
                }
                fx_diff_operations.append(fx_operation)

        # Add FX operations to main list
        operations_data.extend(fx_diff_operations)

        # Sort by date and priority
        operations_data.sort(key=lambda x: (x['date'], x.get('priority', 0)))

        return operations_data

    def create_journal_entries(self, operations_data: List[Dict], instrument: Instrument) -> int:
        """
        Create Journal entries from the final accrual results.

        Args:
            operations_data: List of processed operations
            instrument: Bond instrument

        Returns:
            Number of journal entries created
        """
        created_count = 0

        # Filter for operations that should create journal entries
        export_operations = {
            'INTEREST_ACCRUAL_BOND': 'ACC',
            'FX_DIF_ACCRUAL_MINUS': 'FXDIF',
            'FX_DIF_ACCRUAL_PLUS': 'FXDIF',
        }

        # Get required objects for journal creation
        try:
            ubo = Ubo.objects.get(ubo_code=operations_data[0].get('ubo', 'DEFAULT'))
        except Ubo.DoesNotExist:
            ubo = Ubo.objects.first()  # Fallback to first UBO

        try:
            custodian = Custodian.objects.get(custodian_code=operations_data[0].get('custodian', 'DEFAULT'))
        except Custodian.DoesNotExist:
            custodian = instrument.custodian

        try:
            partner = Partner.objects.get(partner_code=operations_data[0].get('partner', 'DEFAULT'))
        except Partner.DoesNotExist:
            partner = Partner.objects.first()  # Fallback to first partner

        for operation in operations_data:
            if operation['operation'] not in export_operations:
                continue

            # Skip zero-value operations
            if abs(operation.get('value', 0)) <= 0.01:
                continue

            try:
                # Get or create operation
                operation_obj = self.get_or_create_operation(operation['operation'])

                # Get or create account
                account = self.get_or_create_account(
                    operation.get('account', f"{custodian.custodian_code}_{instrument.currency.currency_code}"),
                    instrument.currency,
                    custodian,
                    ubo
                )

                # Get or create instrument for this operation
                if operation['operation'].startswith('FX_DIF_ACCRUAL'):
                    # For FX operations, use RON currency and special instrument
                    ron_currency = Currency.objects.get(currency_code='RON')
                    operation_instrument = self.get_or_create_instrument(
                        operation.get('instrument', f"{custodian.custodian_code}_{instrument.symbol}"),
                        custodian,
                        ron_currency
                    )
                else:
                    operation_instrument = instrument

                # Create transaction ID
                op_symbol = export_operations[operation['operation']]
                transaction_id = f"{instrument.symbol} {op_symbol} {operation['date']}"

                # Create or update journal entry
                _, created = Journal.objects.update_or_create(
                    ubo=ubo,
                    custodian=custodian,
                    account=account,
                    transactionid=transaction_id,
                    defaults={
                        'operation': operation_obj,
                        'partner': partner,
                        'instrument': operation_instrument,
                        'date': operation['date'],
                        'value': operation.get('value', 0),
                        'value_ron': operation.get('value_ron', operation.get('value', 0) * operation.get('bnr', 1.0)),
                        'bnr': operation.get('bnr', 1.0),
                        'quantity': operation.get('quantity', 0),
                        'details': operation.get('details', f"{operation['operation']} {instrument.symbol}"),
                        'storno': False,
                        'lock': False,
                    }
                )

                if created:
                    created_count += 1
                    logger.debug(f"Created journal entry: {transaction_id}")
                else:
                    logger.debug(f"Updated journal entry: {transaction_id}")

            except Exception as e:
                logger.error(f"Error creating journal entry for {operation['operation']}: {str(e)}", exc_info=True)

        logger.info(f"Created {created_count} journal entries for {instrument.symbol}")
        return created_count

    def get_or_create_operation(self, operation_code: str) -> Operation:
        """Get or create an Operation object"""
        try:
            return Operation.objects.get(operation_code=operation_code)
        except Operation.DoesNotExist:
            logger.warning(f"Operation {operation_code} not found, creating default")

            # Create default accounting if needed
            default_accounting, _ = Accounting.objects.get_or_create(
                account_code='000',
                defaults={
                    'account_name': 'DEFAULT_NO_VALUE',
                    'has_currency': False,
                    'has_custodian_debit': True,
                    'has_custodian_credit': True,
                    'has_partner_debit': True,
                    'has_partner_credit': True,
                    'has_symbol': False,
                    'has_dot': False,
                }
            )

            return Operation.objects.create(
                operation_code=operation_code,
                operation_name=operation_code,
                debit=default_accounting,
                credit=default_accounting,
            )

    def get_or_create_account(self, account_code: str, currency: Currency,
                            custodian: Custodian, ubo: Ubo) -> Account:
        """Get or create an Account object"""
        try:
            return Account.objects.get(account_code=account_code)
        except Account.DoesNotExist:
            logger.warning(f"Account {account_code} not found, creating")
            return Account.objects.create(
                account_code=account_code,
                account_name=account_code,
                currency=currency,
                custodian=custodian,
                ubo=ubo,
            )

    def get_or_create_instrument(self, symbol: str, custodian: Custodian,
                               currency: Currency) -> Instrument:
        """Get or create an Instrument object"""
        try:
            return Instrument.objects.get(symbol=symbol, custodian=custodian)
        except Instrument.DoesNotExist:
            logger.warning(f"Instrument {symbol} not found, creating")

            # Truncate isin to fit max_length=12 constraint
            isin_value = symbol[:12] if len(symbol) <= 12 else symbol[:11] + "+"

            return Instrument.objects.create(
                symbol=symbol,
                name=symbol,
                isin=isin_value,
                custodian=custodian,
                currency=currency,
                type='BOND',
                sector='UNKNOWN',
                country='UNKNOWN',
            )

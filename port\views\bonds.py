from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend

from port.models import BondAccrual
from port.serializers import BondAccrualResultSerializer
from port.services.provider.bond_accruals_service import BondAccrualsService
from port.services.provider.bonds_service import BondService
from port.services.storage.ibkr.ibkr_journal_storage import IbkrJournalStorage
from port.services.storage.ibkr.improved_ibkr_journal_storage import ImprovedIbkrJournalStorage


class BondAccrualResultViewSet(viewsets.ModelViewSet):
    queryset = BondAccrual.objects.all().order_by('-date')
    serializer_class = BondAccrualResultSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter, filters.SearchFilter]
    filterset_fields = ['ubo', 'custodian', 'partner', 'account', 'instrument', 'currency', 'operation']
    search_fields = ['details', 'operation__operation_code', 'instrument__symbol', 'custodian__custodian_code', 'account__account_code', 'partner__partner_code', 'ubo__ubo_code', 'currency__currency_code',]
    ordering_fields = ['date', 'value', 'accruals_total']
    ordering = ['-date']

    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        results, count, errors = BondAccrualsService.calculate_bond_accruals()
        if errors:
            errors = list(set(errors))
        if results:
            return Response({"message": f"{count} results saved.", "errors": errors}, status=status.HTTP_201_CREATED)
        return Response({"message": "No results calculated."}, status=status.HTTP_204_NO_CONTENT)

    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

    @action(detail=False, methods=['post'], url_path='recalculate')
    def recalculate_and_save(self, request):
        results, count, errors = BondAccrualsService.calculate_bond_accruals()
        if errors:
            errors = list(set(errors))
        if results:
            return Response({"message": f"{count} results saved.", "errors": errors}, status=status.HTTP_201_CREATED)
        return Response({"message": "No results calculated."}, status=status.HTTP_204_NO_CONTENT)

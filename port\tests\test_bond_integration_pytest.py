"""
Pytest integration tests for bond accrual calculations
"""
import pytest
from datetime import date
from decimal import Decimal

from port.models import Journal, BondAccrual
from port.services.provider.bonds_service import BondService


@pytest.mark.bond_accruals
@pytest.mark.integration
class TestBondAccrualIntegration:
    """Integration tests for bond accrual calculations"""
    
    def test_complete_bond_accrual_flow(self, bond_journals, clean_accrual_data):
        """Test the complete bond accrual calculation flow"""
        # Run bond accrual calculation
        results, count, errors = BondService.calculate_bond_accruals()
        
        # Basic assertions
        assert isinstance(results, list), "Results should be a list"
        assert isinstance(count, int), "Count should be an integer"
        assert isinstance(errors, list), "Errors should be a list"
        assert count >= 0, "Count should be non-negative"
        assert len(results) > 0, "Should have calculation results"
        
        # Check for expected operation types
        operations_found = set(r.get('operation') for r in results)
        assert 'INTEREST_ACCRUAL_BOND' in operations_found, "Should create interest accrual operations"
        
        # Verify data structure
        for result in results:
            assert 'date' in result, "Result should have date"
            assert 'symbol' in result, "Result should have symbol"
            assert 'operation' in result, "Result should have operation"
            assert isinstance(result['date'], date), "Date should be date object"
    
    def test_journal_entries_creation(self, bond_journals, clean_accrual_data):
        """Test that journal entries are created correctly"""
        # Get initial count
        initial_count = Journal.objects.count()
        
        # Run calculation
        results, count, errors = BondService.calculate_bond_accruals()
        
        # Check journal entries were created
        accrual_journals = Journal.objects.filter(
            operation__operation_code__in=[
                'INTEREST_ACCRUAL_BOND', 'FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS'
            ]
        )
        
        # Should have created some accrual journal entries
        if any(r.get('operation') == 'INTEREST_ACCRUAL_BOND' for r in results):
            assert accrual_journals.count() > 0, "Should create accrual journal entries"
            
            # Verify journal structure
            for journal in accrual_journals:
                assert journal.date is not None
                assert journal.operation is not None
                assert journal.instrument is not None
                assert journal.currency is not None
    
    def test_bond_accrual_table_population(self, bond_journals, clean_accrual_data):
        """Test that BondAccrual table is populated"""
        # Run calculation
        results, count, errors = BondService.calculate_bond_accruals()
        
        # Check BondAccrual records
        bond_accruals = BondAccrual.objects.all()
        assert bond_accruals.count() > 0, "Should create BondAccrual records"
        
        # Verify record structure
        for accrual in bond_accruals:
            assert accrual.date is not None
            assert accrual.instrument is not None
            assert accrual.operation is not None


@pytest.mark.bond_accruals
@pytest.mark.unit
class TestBondAccrualComponents:
    """Unit tests for individual bond accrual components"""
    
    def test_face_value_multiplication(self, bond_journals, us_treasury_bond):
        """Test face value multiplication in grouping"""
        # Get journals for the bond
        symbol_bonds = Journal.objects.filter(instrument=us_treasury_bond)
        
        # Test grouping with face value multiplication
        grouped = BondService.group_bounds_by_symbols_and_date(symbol_bonds)
        
        assert len(grouped) > 0, "Should have grouped data"
        
        # Check face value multiplication
        for group in grouped:
            quantity = group.get('quantity', 0)
            if quantity != 0:
                # Should be multiple of face value (1000)
                assert quantity % 1000 == 0, f"Quantity {quantity} should be multiple of 1000"
    
    def test_accrual_date_generation(self, us_treasury_bond):
        """Test accrual date generation"""
        journal_group = [{
            'date': date(2024, 6, 15),
            'symbol': 'T 4 3/8 11/30/28',
            'operation': 'BOND_BUY_BROKER',
            'quantity': 10000,
            'value': 95000
        }]
        
        # Test accrual adjustment
        adjusted = BondService.adjust_accruals(journal_group, us_treasury_bond)
        
        # Should generate accrual operations
        accrual_ops = [op for op in adjusted if op.get('operation') == 'INTEREST_ACCRUAL_BOND']
        assert len(accrual_ops) > 0, "Should generate accrual operations"
        
        for accrual in accrual_ops:
            assert accrual.get('operation') == 'INTEREST_ACCRUAL_BOND'
            assert accrual.get('date') is not None
    
    @pytest.mark.parametrize("fx_diff,expected_operation", [
        (50.0, 'FX_DIF_ACCRUAL_PLUS'),
        (-25.0, 'FX_DIF_ACCRUAL_MINUS'),
        (0.0, None),  # No FX operation for zero difference
    ])
    def test_fx_difference_operations(self, fx_diff, expected_operation):
        """Test FX difference operation creation"""
        grouped_bonds = [{
            'date': date(2024, 6, 15),
            'symbol': 'T 4 3/8 11/30/28',
            'operation': 'INTEREST_ACCRUAL_BOND',
            'fx_diff': fx_diff,
            'currency': 'USD',
            'custodian': 'IBKR',
            'partner': 'TEST_PARTNER',
            'ubo': 'TEST_UBO',
            'details': 'Monthly accrual'
        }]
        
        fx_operations = BondService.create_fx_diff_operations(grouped_bonds)
        
        if expected_operation:
            assert len(fx_operations) == 1, "Should create one FX operation"
            fx_op = fx_operations[0]
            assert fx_op.get('operation') == expected_operation
            assert fx_op.get('currency') == 'RON'
            assert fx_op.get('value') == abs(fx_diff)
        else:
            assert len(fx_operations) == 0, "Should not create FX operations for zero difference"
    
    def test_incremental_accrual_calculation(self):
        """Test incremental accrual calculations"""
        journal_group = [
            {
                'date': date(2024, 6, 15),
                'operation': 'INTEREST_ACCRUAL_BOND',
                'accruals_total': 100.0,
                'accrual_settled': 0.0
            },
            {
                'date': date(2024, 7, 15),
                'operation': 'INTEREST_ACCRUAL_BOND',
                'accruals_total': 200.0,
                'accrual_settled': 0.0
            }
        ]
        
        calculated = BondService.compute_incremental_accruals(journal_group)
        
        accrual_ops = [op for op in calculated if op.get('operation') == 'INTEREST_ACCRUAL_BOND']
        assert len(accrual_ops) >= 2
        
        # Check incremental calculations
        for i, op in enumerate(accrual_ops):
            accrual_incremental = op.get('accrual_incremental')
            assert accrual_incremental is not None
            
            if i == 0:
                assert accrual_incremental == 100.0
            elif i == 1:
                assert accrual_incremental == 100.0  # 200 - 100


@pytest.mark.bond_accruals
@pytest.mark.comparison
class TestOriginalVsRefactoredComparison:
    """Tests comparing original vs refactored implementations"""
    
    def test_operation_types_match_original_pattern(self, bond_journals, clean_accrual_data):
        """Test that operation types match original implementation patterns"""
        # Run refactored implementation
        results, count, errors = BondService.calculate_bond_accruals()
        
        # Check for expected operation patterns from original
        operations_found = set(r.get('operation') for r in results)
        
        # Should have original bond operations
        bond_operations = {'BOND_BUY_BROKER', 'BOND_SELL_BROKER'}
        found_bond_ops = bond_operations.intersection(operations_found)
        assert len(found_bond_ops) > 0, "Should have original bond operations"
        
        # Should have generated accrual operations
        assert 'INTEREST_ACCRUAL_BOND' in operations_found, "Should generate interest accrual operations"
        
        # Check operation structure matches original patterns
        for result in results:
            if result.get('operation') == 'INTEREST_ACCRUAL_BOND':
                # Should have accrual-specific fields
                assert 'accruals_total' in result or 'accrual_incremental' in result
                assert 'accrual' in result.get('details', '').lower()
            
            elif result.get('operation') in ['FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS']:
                # FX operations should be in RON
                assert result.get('currency') == 'RON'
                assert result.get('details', '').startswith('FxDifAcc')
    
    def test_data_flow_matches_original_steps(self, bond_journals, clean_accrual_data):
        """Test that data flows through same steps as original"""
        # Run calculation and verify each step was executed
        results, count, errors = BondService.calculate_bond_accruals()
        
        # Verify face value multiplication was applied
        bond_ops = [r for r in results if r.get('operation') in ['BOND_BUY_BROKER', 'BOND_SELL_BROKER']]
        for op in bond_ops:
            quantity = op.get('quantity', 0)
            if quantity != 0:
                assert quantity % 1000 == 0, "Face value multiplication should be applied"
        
        # Verify accrual operations were generated
        accrual_ops = [r for r in results if r.get('operation') == 'INTEREST_ACCRUAL_BOND']
        assert len(accrual_ops) > 0, "Should generate accrual operations"
        
        # Verify data is sorted (like original)
        dates = [r.get('date') for r in results if r.get('date')]
        assert dates == sorted(dates), "Results should be sorted by date"
    
    def test_journal_creation_matches_original_pattern(self, bond_journals, clean_accrual_data):
        """Test that journal creation matches original implementation"""
        # Run calculation
        results, count, errors = BondService.calculate_bond_accruals()
        
        # Get created journals
        created_journals = Journal.objects.filter(
            operation__operation_code__in=[
                'INTEREST_ACCRUAL_BOND', 'FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS'
            ]
        )
        
        # Should create journals for accrual operations
        interest_results = [r for r in results if r.get('operation') == 'INTEREST_ACCRUAL_BOND']
        if interest_results:
            assert created_journals.count() > 0, "Should create journal entries"
            
            # Verify journal structure matches expectations
            for journal in created_journals:
                assert journal.operation.operation_code in [
                    'INTEREST_ACCRUAL_BOND', 'FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS'
                ]
                assert journal.date is not None
                assert journal.instrument is not None


@pytest.mark.bond_accruals
@pytest.mark.slow
def test_performance_benchmark(bond_journals, clean_accrual_data):
    """Basic performance test for bond accrual calculation"""
    import time
    
    start_time = time.time()
    results, count, errors = BondService.calculate_bond_accruals()
    execution_time = time.time() - start_time
    
    # Should complete in reasonable time
    assert execution_time < 30, f"Calculation took too long: {execution_time:.2f} seconds"
    assert len(results) > 0, "Should produce results"
    assert len(errors) == 0, f"Should not have errors: {errors}"


@pytest.mark.bond_accruals
def test_error_handling_robustness(bond_journals, clean_accrual_data):
    """Test error handling and robustness"""
    # Run calculation
    results, count, errors = BondService.calculate_bond_accruals()
    
    # Should handle gracefully
    assert isinstance(results, list)
    assert isinstance(count, int)
    assert isinstance(errors, list)
    assert count >= 0
    
    # Errors should be informative if they exist
    for error in errors:
        assert isinstance(error, str)
        assert len(error) > 0


# Fixtures for specific test scenarios
@pytest.fixture
def multiple_bonds_scenario(db, currencies, custodian, ubo, partner, accounts, operations):
    """Create scenario with multiple bonds for comprehensive testing"""
    # Create multiple bond instruments
    bonds = []
    
    # US Treasury
    us_bond = Instrument.objects.create(
        symbol='T 4 3/8 11/30/28',
        instrument_name='US Treasury Bond',
        currency=currencies['USD'],
        custodian=custodian,
        face_value=1000,
        interest=4.375,
        maturity=date(2028, 11, 30),
        bond_issue=date(2018, 11, 30),
        bond_coupon_count=2,
        convention='30/360',
        calendar='TARGET'
    )
    bonds.append(us_bond)
    
    # Romanian Bond
    ro_bond = Instrument.objects.create(
        symbol='ROMANI 5 1/2 09/18/28',
        instrument_name='Romanian Bond',
        currency=currencies['EUR'],
        custodian=custodian,
        face_value=1000,
        interest=5.5,
        maturity=date(2028, 9, 18),
        bond_issue=date(2018, 9, 18),
        bond_coupon_count=1,
        convention='ACT/ACT',
        calendar='TARGET'
    )
    bonds.append(ro_bond)
    
    # Create journal entries for both bonds
    journals = []
    
    # US Bond transactions
    journals.append(Journal.objects.create(
        ubo=ubo, custodian=custodian, partner=partner,
        account=accounts['USD'], instrument=us_bond,
        operation=operations['BOND_BUY_BROKER'],
        date=date(2024, 1, 15), currency=currencies['USD'],
        quantity=10, value=95000, details='US bond purchase'
    ))
    
    # Romanian Bond transactions
    journals.append(Journal.objects.create(
        ubo=ubo, custodian=custodian, partner=partner,
        account=accounts['EUR'], instrument=ro_bond,
        operation=operations['BOND_BUY_BROKER'],
        date=date(2024, 2, 10), currency=currencies['EUR'],
        quantity=5, value=48000, details='Romanian bond purchase'
    ))
    
    return {'bonds': bonds, 'journals': journals}


@pytest.mark.bond_accruals
@pytest.mark.integration
def test_multiple_bonds_calculation(multiple_bonds_scenario, clean_accrual_data):
    """Test calculation with multiple different bonds"""
    # Run calculation
    results, count, errors = BondService.calculate_bond_accruals()
    
    # Should handle multiple bonds
    symbols_found = set(r.get('symbol') for r in results)
    assert len(symbols_found) >= 1, "Should process multiple bond symbols"
    
    # Should create accruals for each bond
    for symbol in symbols_found:
        symbol_results = [r for r in results if r.get('symbol') == symbol]
        symbol_accruals = [r for r in symbol_results if r.get('operation') == 'INTEREST_ACCRUAL_BOND']
        # Each bond should have some accrual operations
        assert len(symbol_accruals) >= 0, f"Should have accruals for {symbol}"

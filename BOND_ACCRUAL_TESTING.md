# Bond Accrual Testing Guide

This document describes the comprehensive pytest-based test suite for comparing the original and refactored bond accrual implementations.

## 📁 Test Files

### Core Test Files
- **`test_bond_accruals_pytest.py`** - Main pytest-based comparison tests
- **`test_bond_integration_pytest.py`** - Integration tests for complete workflows
- **`conftest.py`** - Shared pytest fixtures and test data setup

### Supporting Files
- **`run_bond_tests.py`** - <PERSON>ript to run tests with different configurations
- **`pytest.ini`** - Pytest configuration with custom markers
- **`BOND_ACCRUAL_TESTING.md`** - This documentation file

## 🧪 Test Categories

### 1. **Unit Tests** (`@pytest.mark.unit`)
Test individual components and methods:
- Face value multiplication
- Accrual date generation
- QuantLib calculations
- Incremental accrual calculations
- FX difference operations

### 2. **Integration Tests** (`@pytest.mark.integration`)
Test complete workflows:
- End-to-end bond accrual calculation
- Journal entry creation
- BondAccrual table population
- Multiple bonds processing

### 3. **Comparison Tests** (`@pytest.mark.comparison`)
Compare original vs refactored implementations:
- Operation types match original patterns
- Data flow follows same steps
- Journal creation matches original
- Mathematical accuracy

### 4. **Performance Tests** (`@pytest.mark.slow`)
Test performance and robustness:
- Execution time benchmarks
- Memory usage
- Error handling

## 🎯 Test Markers

Use pytest markers to run specific test categories:

```bash
# Run all bond accrual tests
pytest -m bond_accruals

# Run only unit tests
pytest -m "bond_accruals and unit"

# Run only integration tests  
pytest -m "bond_accruals and integration"

# Run comparison tests
pytest -m "bond_accruals and comparison"

# Run fast tests (exclude slow ones)
pytest -m "bond_accruals and not slow"
```

## 🔧 Fixtures

### Core Fixtures
- **`currencies`** - Creates USD, RON, EUR currencies
- **`custodian`** - Creates IBKR custodian
- **`ubo`** - Creates test UBO
- **`partner`** - Creates test partner
- **`operations`** - Creates all bond-related operations
- **`accounts`** - Creates accounts for each currency

### Bond-Specific Fixtures
- **`us_treasury_bond`** - Creates US Treasury bond instrument
- **`bond_journals`** - Creates test bond journal entries
- **`clean_accrual_data`** - Cleans accrual data before/after tests

### Scenario Fixtures
- **`multiple_bonds_scenario`** - Creates complex multi-bond scenario

## 🚀 Running Tests

### Quick Start
```bash
# Run all bond accrual tests
pytest -m bond_accruals -v

# Run with coverage
pytest -m bond_accruals --cov=port.services.provider.bonds_service

# Run specific test file
pytest port/tests/test_bond_accruals_pytest.py -v
```

### Using the Test Runner Script
```bash
# Run all test configurations
python run_bond_tests.py

# This will run:
# - All bond accrual tests
# - Integration tests
# - Unit tests
# - Comparison tests
# - Performance tests
```

### Manual Test Execution
```bash
# Run specific test class
pytest port/tests/test_bond_accruals_pytest.py::TestBondAccrualOperations -v

# Run specific test method
pytest port/tests/test_bond_accruals_pytest.py::TestBondAccrualOperations::test_interest_accrual_operations_created -v

# Run with specific markers
pytest -m "bond_accruals and unit" -v --tb=short
```

## ✅ What the Tests Verify

### 1. **Operation Creation**
- ✅ `INTEREST_ACCRUAL_BOND` operations are created
- ✅ `FX_DIF_ACCRUAL_PLUS`/`MINUS` operations are created when appropriate
- ✅ Operations have correct structure and fields

### 2. **Journal Entry Creation**
- ✅ Journal entries are created in the database
- ✅ Journal entries have correct relationships
- ✅ Values match calculated results

### 3. **Data Processing Steps**
- ✅ Face value multiplication is applied
- ✅ Accrual dates are generated correctly
- ✅ QuantLib calculations are performed
- ✅ Incremental accruals are calculated
- ✅ FX differences are handled
- ✅ Data is sorted properly

### 4. **Mathematical Accuracy**
- ✅ Calculations are mathematically correct
- ✅ Cumulative totals are accurate
- ✅ Incremental values are computed correctly
- ✅ FX conversions are applied

### 5. **Data Consistency**
- ✅ Data remains consistent throughout pipeline
- ✅ Relationships are maintained
- ✅ No data corruption occurs

## 📊 Expected Test Results

### Successful Test Run
```
========================= test session starts =========================
collected 15 items

test_bond_accruals_pytest.py::TestBondAccrualOperations::test_interest_accrual_operations_created PASSED
test_bond_accruals_pytest.py::TestBondAccrualOperations::test_fx_difference_operations_created PASSED
test_bond_accruals_pytest.py::TestBondAccrualOperations::test_journal_entries_created PASSED
test_bond_accruals_pytest.py::TestDataFlowSteps::test_face_value_multiplication PASSED
test_bond_accruals_pytest.py::TestDataFlowSteps::test_accrual_date_generation PASSED
test_bond_accruals_pytest.py::TestDataFlowSteps::test_quantlib_calculations PASSED
test_bond_accruals_pytest.py::TestDataFlowSteps::test_fx_difference_creation PASSED
test_bond_accruals_pytest.py::TestCalculationAccuracy::test_incremental_accrual_calculations PASSED
test_bond_accruals_pytest.py::TestCalculationAccuracy::test_calculation_pipeline_completeness PASSED
test_bond_accruals_pytest.py::TestOriginalVsRefactoredComparison::test_operation_types_match_original_pattern PASSED
test_bond_accruals_pytest.py::TestOriginalVsRefactoredComparison::test_data_flow_matches_original_steps PASSED
test_bond_accruals_pytest.py::TestOriginalVsRefactoredComparison::test_journal_creation_matches_original_pattern PASSED

========================= 12 passed in 5.23s =========================
```

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Errors**
   ```bash
   # Ensure Django settings are correct
   export DJANGO_SETTINGS_MODULE=nch.settings
   ```

2. **Missing Test Data**
   ```bash
   # Run migrations first
   python manage.py migrate
   ```

3. **Import Errors**
   ```bash
   # Ensure you're in the project root
   cd /path/to/your/project
   ```

### Debug Mode
```bash
# Run with verbose output and no capture
pytest -m bond_accruals -v -s --tb=long

# Run single test with debugging
pytest port/tests/test_bond_accruals_pytest.py::test_specific_test -v -s --pdb
```

## 📈 Test Coverage

To check test coverage:
```bash
# Install coverage
pip install pytest-cov

# Run with coverage
pytest -m bond_accruals --cov=port.services.provider.bonds_service --cov-report=html

# View coverage report
open htmlcov/index.html
```

## 🔄 Continuous Integration

For CI/CD pipelines, use:
```bash
# Fast tests only (exclude slow performance tests)
pytest -m "bond_accruals and not slow" --tb=short

# With JUnit XML output for CI
pytest -m bond_accruals --junitxml=test-results.xml
```

## 📝 Adding New Tests

### Test Structure
```python
@pytest.mark.bond_accruals
@pytest.mark.unit  # or integration, comparison, slow
def test_new_functionality(fixture1, fixture2, clean_accrual_data):
    """Test description"""
    # Arrange
    # Act
    # Assert
```

### Using Fixtures
```python
def test_with_custom_data(currencies, custodian, clean_accrual_data):
    # Test uses shared fixtures
    assert currencies['USD'].currency_code == 'USD'
```

### Parametrized Tests
```python
@pytest.mark.parametrize("input_value,expected", [
    (100, 'PLUS'),
    (-50, 'MINUS'),
    (0, None)
])
def test_fx_operations(input_value, expected):
    # Test with multiple parameter sets
```

## 🎯 Success Criteria

The tests pass when:

1. **All operations are created** - Both `INTEREST_ACCRUAL_BOND` and `FX_DIF_ACCRUAL_*` operations
2. **Journal entries are created** - Actual database records are inserted
3. **Calculations are accurate** - Mathematical results match expectations
4. **Data flow is complete** - All processing steps are executed
5. **Performance is acceptable** - Tests complete within reasonable time
6. **Error handling works** - Graceful handling of edge cases

## 📞 Support

If tests fail:

1. **Check the test output** for specific error messages
2. **Run individual tests** to isolate issues
3. **Verify test data setup** using fixtures
4. **Check database state** before and after tests
5. **Compare with working implementation** if available

The test suite provides comprehensive coverage to ensure the refactored bond accrual service produces identical results to the original implementation.

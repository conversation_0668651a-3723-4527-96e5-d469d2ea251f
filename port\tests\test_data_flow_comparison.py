"""
Test to verify the data flow matches between original and refactored implementations
"""
from datetime import date
from django.test import TestCase
from unittest.mock import patch, MagicMock
import pandas as pd

from port.models import (
    Journal, Instrument, Currency, Custodian, Ubo, Partner, Account, Operation
)
from port.services.provider.bonds_service import BondService


class DataFlowComparisonTest(TestCase):
    """Test that data flows through the same logical steps in both implementations"""
    
    def setUp(self):
        """Set up test data"""
        self.setup_test_data()
    
    def setup_test_data(self):
        """Create minimal test data for comparison"""
        # Create entities
        self.usd = Currency.objects.create(currency_code='USD')
        self.ron = Currency.objects.create(currency_code='RON')
        
        self.custodian = Custodian.objects.create(
            custodian_code='IBKR',
            custodian_name='Interactive Brokers'
        )
        
        self.ubo = Ubo.objects.create(ubo_code='TEST', ubo_name='Test UBO')
        self.partner = Partner.objects.create(partner_code='PARTNER', partner_name='Test Partner')
        
        self.account = Account.objects.create(
            account_code='IBKR_USD',
            account_name='IBKR USD',
            currency=self.usd,
            custodian=self.custodian,
            ubo=self.ubo
        )
        
        # Create operations
        self.bond_buy = Operation.objects.create(
            operation_code='BOND_BUY_BROKER',
            operation_name='Bond Buy'
        )
        
        # Create bond
        self.bond = Instrument.objects.create(
            symbol='TEST_BOND',
            instrument_name='Test Bond',
            currency=self.usd,
            custodian=self.custodian,
            face_value=1000,
            interest=5.0,
            maturity=date(2025, 12, 31),
            bond_issue=date(2020, 1, 1),
            bond_coupon_count=2,
            convention='30/360',
            calendar='TARGET'
        )
        
        # Create journal entry
        Journal.objects.create(
            ubo=self.ubo,
            custodian=self.custodian,
            partner=self.partner,
            account=self.account,
            instrument=self.bond,
            operation=self.bond_buy,
            date=date(2024, 6, 15),
            currency=self.usd,
            quantity=10,
            value=95000,
            details='Test bond purchase'
        )

    def test_step_1_data_query(self):
        """Test that data query step works correctly"""
        # Test the get_bonds_data method
        bond_journal_qs, bnr_qs, bnr_eom_qs, bond_info_qs = BondService.get_bonds_data()
        
        # Should return querysets
        self.assertIsNotNone(bond_journal_qs, "Should return bond journal queryset")
        self.assertIsNotNone(bond_info_qs, "Should return bond info queryset")
        
        # Should have our test data
        self.assertGreater(bond_journal_qs.count(), 0, "Should have bond journal entries")
        self.assertGreater(bond_info_qs.count(), 0, "Should have bond instruments")

    def test_step_2_face_value_multiplication(self):
        """Test that face value multiplication is applied"""
        # Get bond journals for our test bond
        symbol_bonds = Journal.objects.filter(instrument=self.bond)
        
        # Group and check face value multiplication
        grouped = BondService.group_bounds_by_symbols_and_date(symbol_bonds)
        
        self.assertGreater(len(grouped), 0, "Should have grouped data")
        
        # Check that quantity is multiplied by face value
        for group in grouped:
            quantity = group.get('quantity', 0)
            if quantity != 0:
                # Original quantity was 10, face value is 1000, so should be 10000
                self.assertEqual(quantity, 10000, 
                               f"Quantity should be multiplied by face value: expected 10000, got {quantity}")

    def test_step_3_accrual_date_generation(self):
        """Test that accrual dates are generated correctly"""
        # Create a sample journal group
        journal_group = [{
            'date': date(2024, 6, 15),
            'symbol': 'TEST_BOND',
            'operation': 'BOND_BUY_BROKER',
            'quantity': 10000,
            'value': 95000
        }]
        
        # Test accrual adjustment
        adjusted = BondService.adjust_accruals(journal_group, self.bond)
        
        # Should have added accrual operations
        accrual_ops = [op for op in adjusted if op.get('operation') == 'INTEREST_ACCRUAL_BOND']
        self.assertGreater(len(accrual_ops), 0, "Should generate accrual operations")
        
        # Check that accrual operations have correct structure
        for accrual in accrual_ops:
            self.assertEqual(accrual.get('operation'), 'INTEREST_ACCRUAL_BOND')
            self.assertIsNotNone(accrual.get('date'), "Accrual should have date")
            self.assertIn('accrual', accrual.get('details', '').lower(), 
                         "Accrual should mention 'accrual' in details")

    def test_step_4_quantlib_calculations(self):
        """Test that QuantLib calculations are performed"""
        # Create journal group with accrual operations
        journal_group = [
            {
                'date': date(2024, 6, 15),
                'symbol': 'TEST_BOND',
                'operation': 'BOND_BUY_BROKER',
                'quantity': 10000,
                'value': 95000
            },
            {
                'date': date(2024, 6, 30),
                'symbol': 'TEST_BOND', 
                'operation': 'INTEREST_ACCRUAL_BOND',
                'quantity': 0,
                'value': 0,
                'quantity_total': 10000
            }
        ]
        
        # Test accrual calculation
        calculated = BondService.calculate_accruals_for_group_optimized(journal_group, self.bond)
        
        # Check that accruals_total is calculated
        accrual_op = next((op for op in calculated if op.get('operation') == 'INTEREST_ACCRUAL_BOND'), None)
        self.assertIsNotNone(accrual_op, "Should have accrual operation")
        
        accruals_total = accrual_op.get('accruals_total')
        self.assertIsNotNone(accruals_total, "Should calculate accruals_total")
        self.assertIsInstance(accruals_total, (int, float), "Accruals should be numeric")

    def test_step_5_incremental_calculations(self):
        """Test that incremental accrual calculations work"""
        # Create sample data with multiple accrual points
        journal_group = [
            {
                'date': date(2024, 6, 15),
                'operation': 'INTEREST_ACCRUAL_BOND',
                'accruals_total': 100.0,
                'accrual_settled': 0.0
            },
            {
                'date': date(2024, 7, 15),
                'operation': 'INTEREST_ACCRUAL_BOND', 
                'accruals_total': 200.0,
                'accrual_settled': 0.0
            }
        ]
        
        # Test incremental calculation
        calculated = BondService.compute_incremental_accruals(journal_group)
        
        # Check incremental values
        for i, op in enumerate(calculated):
            if op.get('operation') == 'INTEREST_ACCRUAL_BOND':
                accrual_incremental = op.get('accrual_incremental')
                self.assertIsNotNone(accrual_incremental, "Should have accrual_incremental")
                
                if i == 0:
                    # First accrual should be the full amount
                    self.assertEqual(accrual_incremental, 100.0)
                elif i == 1:
                    # Second accrual should be the difference
                    self.assertEqual(accrual_incremental, 100.0)

    def test_step_6_fx_difference_creation(self):
        """Test that FX difference operations are created"""
        # Create sample data with FX differences
        grouped_bonds = [
            {
                'date': date(2024, 6, 15),
                'symbol': 'TEST_BOND',
                'operation': 'INTEREST_ACCRUAL_BOND',
                'fx_diff': 50.0,  # Positive FX difference
                'currency': 'USD',
                'custodian': 'IBKR',
                'details': 'Monthly accrual TEST_BOND'
            },
            {
                'date': date(2024, 7, 15),
                'symbol': 'TEST_BOND',
                'operation': 'INTEREST_ACCRUAL_BOND', 
                'fx_diff': -25.0,  # Negative FX difference
                'currency': 'USD',
                'custodian': 'IBKR',
                'details': 'Monthly accrual TEST_BOND'
            }
        ]
        
        # Test FX operation creation
        fx_operations = BondService.create_fx_diff_operations(grouped_bonds)
        
        # Should create 2 FX operations
        self.assertEqual(len(fx_operations), 2, "Should create FX operations for non-zero fx_diff")
        
        # Check first FX operation (positive)
        fx_plus = fx_operations[0]
        self.assertEqual(fx_plus.get('operation'), 'FX_DIF_ACCRUAL_PLUS')
        self.assertEqual(fx_plus.get('currency'), 'RON')
        self.assertEqual(fx_plus.get('value'), 50.0)
        self.assertTrue(fx_plus.get('details', '').startswith('FxDifAcc'))
        
        # Check second FX operation (negative)
        fx_minus = fx_operations[1]
        self.assertEqual(fx_minus.get('operation'), 'FX_DIF_ACCRUAL_MINUS')
        self.assertEqual(fx_minus.get('currency'), 'RON')
        self.assertEqual(fx_minus.get('value'), 25.0)  # Should be absolute value

    def test_step_7_data_merging_and_sorting(self):
        """Test that data is properly merged and sorted"""
        # Create sample data
        base_operations = [
            {
                'date': date(2024, 6, 20),
                'operation': 'INTEREST_ACCRUAL_BOND',
                'priority': 4,
                'fx_diff': 0
            },
            {
                'date': date(2024, 6, 15),
                'operation': 'BOND_BUY_BROKER',
                'priority': 1,
                'fx_diff': 0
            }
        ]
        
        fx_operations = [
            {
                'date': date(2024, 6, 18),
                'operation': 'FX_DIF_ACCRUAL_PLUS',
                'priority': 5,
                'fx_diff': 0
            }
        ]
        
        # Merge operations (simulate the merging step)
        all_operations = base_operations + fx_operations
        
        # Sort by date and priority
        all_operations.sort(key=lambda x: (x['date'], x.get('priority', 0)))
        
        # Check sorting
        expected_order = ['BOND_BUY_BROKER', 'FX_DIF_ACCRUAL_PLUS', 'INTEREST_ACCRUAL_BOND']
        actual_order = [op['operation'] for op in all_operations]
        
        self.assertEqual(actual_order, expected_order, 
                        "Operations should be sorted by date then priority")

    def test_step_8_journal_entry_creation(self):
        """Test that journal entries are created correctly"""
        # Run the full calculation to test journal creation
        results, _, _ = BondService.calculate_bond_accruals()
        
        # Check that journal entries were created
        created_journals = Journal.objects.filter(
            operation__operation_code__in=[
                'INTEREST_ACCRUAL_BOND', 'FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS'
            ]
        )
        
        # Should have created some journal entries
        if any(r.get('operation') == 'INTEREST_ACCRUAL_BOND' for r in results):
            self.assertGreater(created_journals.count(), 0, 
                             "Should create journal entries for accrual operations")

    def test_complete_data_flow(self):
        """Test the complete data flow from start to finish"""
        # Run the complete calculation
        results, count, errors = BondService.calculate_bond_accruals()
        
        # Verify complete flow
        self.assertIsInstance(results, list, "Should return list of results")
        self.assertIsInstance(count, int, "Should return count")
        self.assertIsInstance(errors, list, "Should return errors list")
        
        # Should have processed our test data
        self.assertGreater(len(results), 0, "Should have results")
        
        # Check that all expected steps were executed
        operations_found = set(r.get('operation') for r in results)
        
        # Should have original operations
        self.assertIn('BOND_BUY_BROKER', operations_found, "Should have original bond operations")
        
        # Should have generated accrual operations
        self.assertIn('INTEREST_ACCRUAL_BOND', operations_found, "Should generate accrual operations")
        
        # Verify data structure consistency
        for result in results:
            # All results should have basic required fields
            required_fields = ['date', 'symbol', 'operation']
            for field in required_fields:
                self.assertIn(field, result, f"Result should have {field}")
            
            # Dates should be date objects
            self.assertIsInstance(result['date'], date, "Date should be date object")
            
            # Operations should be strings
            self.assertIsInstance(result['operation'], str, "Operation should be string")

    def test_error_handling_in_data_flow(self):
        """Test that errors are handled gracefully throughout the data flow"""
        # Test with minimal data to ensure no crashes
        results, count, errors = BondService.calculate_bond_accruals()
        
        # Should not crash and should return valid data structures
        self.assertIsInstance(results, list)
        self.assertIsInstance(count, int)
        self.assertIsInstance(errors, list)
        
        # Count should be non-negative
        self.assertGreaterEqual(count, 0)
        
        # If there are errors, they should be informative
        for error in errors:
            self.assertIsInstance(error, str, "Errors should be strings")

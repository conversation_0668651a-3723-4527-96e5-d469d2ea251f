import json
import pytest
import os
from datetime import date
from decimal import Decimal

from port.models import (
    Journal, Instrument, Currency, Custodian, Ubo, Partner, Account, Operation, BondAccrual, Bnr
)


@pytest.fixture
def client_fixture():
    from django.test.client import Client
    return Client()

@pytest.fixture
def bnr_test_data():
    current_dir = os.path.dirname(os.path.abspath(__file__))
    json_path = os.path.join(current_dir, '..', 'test_input_files', 'bnr_10_days.json')
    
    with open(json_path, 'r') as file:
        return json.load(file)

@pytest.fixture
def bnr_two_days_weekend_test_data():
    current_dir = os.path.dirname(os.path.abspath(__file__))
    json_path = os.path.join(current_dir, '..', 'test_input_files', 'bnr_2_days_with_weekend.json')

    with open(json_path, 'r') as file:
        return json.load(file)

@pytest.fixture
def bnr_single_day_data():
    return {
        "@date": "2025-01-01",
        "Rate": [
            {
                "@currency": "USD",
                "#text": "4.0000",
                "@multiplier": "1"
            },
            {
                "@currency": "EUR",
                "#text": "4.5000",
                "@multiplier": "1"
            },
            {
                "@currency": "GBP",
                "#text": "5.2000",
                "@multiplier": "1"
            }
        ]
    }


# Bond Accrual Test Fixtures

@pytest.fixture
def currencies(db):
    """Create test currencies"""
    usd, _ = Currency.objects.get_or_create(
        currency_code='USD',
        defaults={'currency_name': 'US Dollar'}
    )
    ron, _ = Currency.objects.get_or_create(
        currency_code='RON',
        defaults={'currency_name': 'Romanian Leu'}
    )
    eur, _ = Currency.objects.get_or_create(
        currency_code='EUR',
        defaults={'currency_name': 'Euro'}
    )
    return {'USD': usd, 'RON': ron, 'EUR': eur}


@pytest.fixture
def custodian(db):
    """Create test custodian"""
    return Custodian.objects.create(
        custodian_code='IBKR',
        custodian_name='Interactive Brokers'
    )


@pytest.fixture
def ubo(db):
    """Create test UBO"""
    return Ubo.objects.create(
        ubo_code='TEST_UBO',
        ubo_name='Test UBO'
    )


@pytest.fixture
def partner(db):
    """Create test partner"""
    return Partner.objects.create(
        partner_code='TEST_PARTNER',
        partner_name='Test Partner'
    )


@pytest.fixture
def operations(db):
    """Create test operations"""
    operation_codes = [
        'BOND_BUY_BROKER', 'BOND_SELL_BROKER', 'BOND_COUPON_RECEIVED_BROKER',
        'INTEREST_ACCRUAL_BOND', 'FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS'
    ]

    ops = {}
    for code in operation_codes:
        ops[code] = Operation.objects.create(
            operation_code=code,
            operation_name=code.replace('_', ' ').title()
        )
    return ops


@pytest.fixture
def accounts(db, currencies, custodian, ubo):
    """Create test accounts"""
    accounts = {}
    for currency_code, currency in currencies.items():
        accounts[currency_code] = Account.objects.create(
            account_code=f'IBKR_{currency_code}',
            account_name=f'IBKR {currency_code} Account',
            currency=currency,
            custodian=custodian,
            ubo=ubo
        )
    return accounts


@pytest.fixture
def us_treasury_bond(db, currencies, custodian):
    """Create US Treasury bond instrument"""
    return Instrument.objects.create(
        symbol='T 4 3/8 11/30/28',
        instrument_name='US Treasury Bond 4.375% 11/30/2028',
        currency=currencies['USD'],
        custodian=custodian,
        face_value=1000,
        interest=4.375,
        maturity=date(2028, 11, 30),
        bond_issue=date(2018, 11, 30),
        bond_coupon_count=2,
        convention='30/360',
        calendar='TARGET'
    )


@pytest.fixture
def bond_journals(db, ubo, custodian, partner, accounts, us_treasury_bond, operations):
    """Create test bond journal entries"""
    journals = []

    # Bond purchase
    journals.append(Journal.objects.create(
        ubo=ubo,
        custodian=custodian,
        partner=partner,
        account=accounts['USD'],
        instrument=us_treasury_bond,
        operation=operations['BOND_BUY_BROKER'],
        date=date(2024, 1, 15),
        currency=us_treasury_bond.currency,
        quantity=10,
        value=95000,
        details='Bond purchase'
    ))

    # Bond sale
    journals.append(Journal.objects.create(
        ubo=ubo,
        custodian=custodian,
        partner=partner,
        account=accounts['USD'],
        instrument=us_treasury_bond,
        operation=operations['BOND_SELL_BROKER'],
        date=date(2024, 6, 15),
        currency=us_treasury_bond.currency,
        quantity=-5,
        value=48000,
        details='Bond sale'
    ))

    return journals


@pytest.fixture
def clean_accrual_data(db):
    """Clean accrual data before and after each test"""
    # Clean before test
    BondAccrual.objects.all().delete()
    Journal.objects.filter(
        operation__operation_code__in=[
            'INTEREST_ACCRUAL_BOND', 'FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS'
        ]
    ).delete()

    yield

    # Clean after test
    BondAccrual.objects.all().delete()
    Journal.objects.filter(
        operation__operation_code__in=[
            'INTEREST_ACCRUAL_BOND', 'FX_DIF_ACCRUAL_PLUS', 'FX_DIF_ACCRUAL_MINUS'
        ]
    ).delete()

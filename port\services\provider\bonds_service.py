import copy
import logging
import QuantLib as ql
from typing import Dict, List, Tuple, Set

from collections import defaultdict
from itertools import chain
from bisect import bisect_right
from django.db import IntegrityError, connection, transaction
from django.db.models import QuerySet, Sum, Func, OuterRef, Max, Subquery, F, Min, Max
from django.db.models.functions import TruncMonth
from django.contrib.postgres.aggregates import ArrayAgg
from dateutil.relativedelta import relativedelta
from datetime import date, datetime
from django.conf import settings

from port.models import Account, Accounting, Bnr, BondAccrual, Currency, Custodian, Instrument, Journal, Operation, Partner, Ubo
from port.services.storage.ibkr.ibkr_journal_storage import IbkrJournalStorage


logger = logging.getLogger(__name__)

class ArrayToString(Func):
    function = 'array_to_string'
    template = "%(function)s(%(expressions)s, ',')"


class BondService:
    PAYMENT_CONVENTION = {
        # 'SimpleDayCounter': ql.SimpleDayCounter(),
        # '30/360': ql.Thirty360(ql.Thirty360.ISMA),
        '30/360': ql.Thirty360(ql.Thirty360.ISDA),
        'ISMA-30/360': ql.Thirty360(ql.Thirty360.European),
        'ACT/ACT': ql.ActualActual(ql.ActualActual.ISMA),
    }

    CALENDAR_MAP = {
        'GovernmentBond': ql.UnitedStates(ql.UnitedStates.GovernmentBond),
        'TARGET': ql.TARGET(),
        'NYSE': ql.UnitedStates(ql.UnitedStates.NYSE),
        'FederalReserve': ql.UnitedStates(ql.UnitedStates.FederalReserve),
        'Settlement': ql.UnitedStates(ql.UnitedStates.Settlement),
    }

    # Performance optimization caches
    _bond_cache: Dict[str, ql.FixedRateBond] = {}
    _schedule_cache: Dict[Tuple, ql.Schedule] = {}
    _bnr_cache: Dict[Tuple[str, date], float] = {}
    _batch_size = getattr(settings, 'BONDS_BATCH_SIZE', 100)
    
    @classmethod
    def calculate_bond_accruals(cls):
        """Optimized bond accruals calculation with caching and bulk operations"""
        logger.info("Starting optimized bond accruals calculation...")

        bond_journal_qs, bnr_qs, bnr_eom_qs, bond_info_qs = cls.get_bonds_data()
        if not bond_journal_qs.exists() and not bond_info_qs.exists():
            logger.info("No bond operations found")
            return [], 0, []

        # Pre-load BNR data into cache
        cls._preload_bnr_cache(bnr_qs, bnr_eom_qs)

        count = 0
        symbols = bond_info_qs.values_list('symbol', flat=True)
        total_symbols = len(symbols)
        final_grouped_bonds = []
        errors = []
        all_bond_accruals = []

        logger.info(f"Processing {total_symbols} bond symbols in batches of {cls._batch_size}")

        # Process symbols in batches
        for i in range(0, total_symbols, cls._batch_size):
            batch_symbols = symbols[i:i + cls._batch_size]
            batch_accruals = []

            for j, symbol in enumerate(batch_symbols):
                try:
                    # Log progress every 10 symbols
                    if (i + j) % 10 == 0:
                        logger.info(f"Processing bond {i + j + 1}/{total_symbols}: {symbol}")

                    symbol_bonds = bond_journal_qs.filter(instrument__symbol=symbol)
                    instrument_symbol = bond_info_qs.get(symbol=symbol)
                    if not symbol_bonds.exists():
                        logger.debug(f"No bond operations found for {symbol}")
                        continue

                    # Process bond with optimized methods
                    grouped_bonds = cls._process_bond_symbol_optimized(
                        symbol_bonds, instrument_symbol, bnr_qs, bnr_eom_qs
                    )

                    final_grouped_bonds.extend(grouped_bonds)

                    if grouped_bonds:
                        # Collect for bulk storage
                        for row in grouped_bonds:
                            accrual_data = cls._prepare_bond_accrual_data(row, instrument_symbol)
                            if accrual_data:
                                batch_accruals.append(accrual_data)
                        count += len(grouped_bonds)

                except Exception as e:
                    logger.error(f"Error processing symbol {symbol}: {str(e)}", exc_info=True)
                    errors.append(symbol)

            # Bulk store batch
            if batch_accruals:
                cls._bulk_store_bond_accruals(batch_accruals)
                all_bond_accruals.extend(batch_accruals)
                logger.info(f"Stored batch of {len(batch_accruals)} bond accruals")

        logger.info(f"Completed processing {total_symbols} symbols, {count} bond entries with {len(errors)} errors")
        if errors:
            logger.warning(f"Errors occurred for symbols: {errors}")

        # Store accrual results as journal entries (critical step that was missing!)
        if final_grouped_bonds:
            logger.info("Storing accrual results as journal entries...")
            cls.save_accrual_results(final_grouped_bonds, errors)
            logger.info("Successfully stored accrual results as journal entries")

        return final_grouped_bonds, count, errors

    @classmethod
    def save_accrual_results(cls, final_grouped_bonds: list[dict], errors: list[str]) -> list:
        if not final_grouped_bonds:
            logger.info("No accrual results to save.")
            return 0

        # Create dictionaries for efficient lookup and safe access
        ubos = {u.ubo_code: u for u in Ubo.objects.all()}
        custodians = {c.custodian_code: c for c in Custodian.objects.all()}
        partners = {p.partner_code: p for p in Partner.objects.all()}
        currencies = {c.currency_code: c for c in Currency.objects.all()}

        for row in final_grouped_bonds:
            # Safe dictionary lookups with error handling
            currency = currencies.get(row['currency'])
            custodian = custodians.get(row['custodian'])
            ubo = ubos.get(row['ubo'])

            # Skip if any required objects are missing
            if not currency:
                logger.warning(f"Currency '{row['currency']}' not found, skipping entry")
                continue
            if not custodian:
                logger.warning(f"Custodian '{row['custodian']}' not found, skipping entry")
                continue
            if not ubo:
                logger.warning(f"UBO '{row['ubo']}' not found, skipping entry")
                continue
            account = BondService.get_account(
                row['account'],
                currency=currency,
                custodian=custodian,
                ubo=ubo,
            )
            instrument = BondService.get_instrument(
                symbol=row['symbol'],
                custodian=custodian,
                currency=currency
            )
            operation = BondService.get_operation(operation_code=row['operation'])

            # Check for partner before proceeding
            partner = partners.get(row['partner'])
            if not partner:
                logger.warning(f"Partner '{row['partner']}' not found, skipping entry")
                continue

            try:
                BondAccrual.objects.update_or_create(
                    ubo_id=ubo.id,
                    custodian_id=custodian.id,
                    partner_id=partner.id,
                    account_id=account.id,
                    instrument_id=instrument.id,
                    operation_id=operation.id,
                    date=row['date'],
                    defaults={
                        'currency_id': currency.id,
                        'details': row.get('details'),
                        'priority': row.get('priority', 1),
                        'quantity': row.get('quantity', 0),
                        'quantity_total': row.get('quantity_total', 0),
                        'value': row.get('value', 0),
                        'accruals_total': row.get('accruals_total', 0),
                        'accrual': row.get('accrual', 0),
                        'accrual_valuta': row.get('accrual_valuta', 0),
                        'accrual_ron': row.get('accrual_ron', 0),
                        'total_accrual_valuta': row.get('total_accrual_valuta', 0),
                        'total_accrual_ron': row.get('total_accrual_ron', 0),
                        'revalue_total': row.get('revalue_total', 0),
                        'total_fx_diff': row.get('total_fx_diff', 0),
                        'fx_diff': row.get('fx_diff', 0),
                        'bnr': row.get('bnr'),
                        'bnr_eom': row.get('bnr_eom'),
                        'coupon_settled': row.get('coupon_settled', 0),
                        'accrual_settled': row.get('accrual_settled', 0),
                        'accrual_incremental': row.get('accrual_incremental', 0),
                    }
                )
            except KeyError as e:
                logger.warning(f"Skipping entry due to missing key: {e}")
                errors.append(instrument.symbol)
            except Exception as e:
                logger.error(f"Unexpected error while preparing entry: {e}")
                errors.append(instrument.symbol)
            else:
                # Create journal entries for INTEREST_ACCRUAL_BOND operations
                if row['operation'] == 'INTEREST_ACCRUAL_BOND':
                    accrual_incremental = row.get('accrual_incremental', 0)
                    logger.debug(f"INTEREST_ACCRUAL_BOND operation found: date={row['date']}, symbol={row['symbol']}, accrual_incremental={accrual_incremental}")

                    if accrual_incremental != 0:
                        cls._create_interest_accrual_journal_entry(row, ubo, custodian, partners, account, instrument, operation)
                    else:
                        logger.warning(f"Skipping INTEREST_ACCRUAL_BOND journal entry for {row['symbol']} on {row['date']} - accrual_incremental is 0")

                # Create FX difference operations if fx_diff != 0
                fx_diff = row.get('fx_diff', 0)
                if fx_diff != 0:
                    cls._create_fx_diff_journal_entry(row, ubo, custodian, partners, currencies, fx_diff)

        return errors

    @classmethod
    def _create_interest_accrual_journal_entry(cls, row: dict, ubo: Ubo, custodian: Custodian,
                                             partners: dict, account: Account, instrument: Instrument,
                                             operation: Operation):
        """Create journal entry for INTEREST_ACCRUAL_BOND operation"""
        try:
            Journal.objects.update_or_create(
                ubo=ubo,
                custodian=custodian,
                partner=partners.get(row['partner']),
                account=account,
                instrument=instrument,
                operation=operation,
                date=row['date'],
                defaults={
                    'transactionid': f"ACCRUAL_{row['date']}_{instrument.symbol}",
                    'details': row.get('details', ''),
                    'quantity': row.get('quantity', 0),
                    'value': row.get('accrual_incremental', 0),
                    'value_ron': row.get('accrual_incremental', 0) * row.get('bnr', 1.0),
                    'bnr': row.get('bnr', 1.0),
                }
            )
            logger.debug(f"Created INTEREST_ACCRUAL_BOND journal entry for {instrument.symbol} on {row['date']}")
        except Exception as e:
            logger.error(f"Error creating INTEREST_ACCRUAL_BOND journal entry: {e}")

    @classmethod
    def _create_fx_diff_journal_entry(cls, row: dict, ubo: Ubo, custodian: Custodian,
                                    partners: dict, currencies: dict, fx_diff: float):
        """Create journal entry for FX_DIF_ACCRUAL_PLUS/MINUS operation"""
        try:
            # Determine operation type based on fx_diff sign
            fx_operation_code = 'FX_DIF_ACCRUAL_PLUS' if fx_diff > 0 else 'FX_DIF_ACCRUAL_MINUS'
            fx_operation = cls.get_operation(operation_code=fx_operation_code)

            # Get RON currency and RON account
            ron_currency = currencies.get('RON')
            if not ron_currency:
                logger.error("RON currency not found, cannot create FX difference journal entry")
                return

            ron_account = cls.get_account(
                f"{custodian.custodian_code}_RON",
                currency=ron_currency,
                custodian=custodian,
                ubo=ubo,
            )

            # Create RON instrument for FX operations
            ron_instrument = cls.get_instrument(
                symbol=f"{custodian.custodian_code}_{row['symbol']}",
                custodian=custodian,
                currency=ron_currency
            )

            Journal.objects.update_or_create(
                ubo=ubo,
                custodian=custodian,
                partner=partners.get(row['partner']),
                account=ron_account,
                instrument=ron_instrument,
                operation=fx_operation,
                date=row['date'],
                defaults={
                    'transactionid': f"FXDIFF_{row['date']}_{row['symbol']}",
                    'details': f"FxDifAcc {row.get('details', '')}",
                    'quantity': 0,
                    'value': abs(fx_diff),  # Store absolute value
                    'value_ron': abs(fx_diff),  # FX operations are already in RON
                    'bnr': 1.0,  # RON to RON rate is 1.0
                }
            )
            logger.debug(f"Created {fx_operation_code} journal entry for {row['symbol']} on {row['date']}")
        except Exception as e:
            logger.error(f"Error creating FX difference journal entry: {e}")

    @staticmethod
    def get_account(account_code: str, currency: Currency, custodian: Custodian, ubo: Ubo) -> Account:
        try:
            return Account.objects.get(account_code=account_code)
        except Account.DoesNotExist:
            logger.error(f"Account not found for {account_code}")
            return Account.objects.get_or_create(
                account_code=account_code,
                account_name=account_code,
                currency=currency,
                custodian=custodian,
                ubo=ubo,
            )[0]
    
    @staticmethod
    def get_instrument(symbol: str, custodian: Custodian, currency: Currency) -> Instrument:
        instrument = Instrument.objects.filter(symbol=symbol, custodian__custodian_code=custodian.custodian_code).first()

        if instrument:
            return instrument

        logger.warning(f"Instrument not found for {symbol}, creating one.")

        try:
            with transaction.atomic():
                # Truncate isin to fit max_length=12 constraint
                isin_value = symbol[:12] if len(symbol) <= 12 else symbol[:11] + "+"

                return Instrument.objects.get_or_create(
                    symbol=symbol,
                    name=symbol,
                    isin=isin_value,
                    custodian=custodian,
                    currency=currency,
                    type='UNKNOWN',
                    sector='UNKNOWN',
                    country='UNKNOWN',
                )[0]
        except IntegrityError:
            # fallback in case of race condition
            return Instrument.objects.get(
                symbol=symbol, custodian__custodian_code=custodian.custodian_code
            )

    @staticmethod
    def get_operation(operation_code: str) -> Operation:
        try:
            return Operation.objects.get(operation_code=operation_code)
        except Operation.DoesNotExist:
            logger.error(f"Operation not found for {operation_code}")
            default_accounting, _ = Accounting.objects.get_or_create(
                account_code='000',
                defaults={
                    'account_name': 'DEFAULT_NO_VALUE',
                    'has_currency': False,
                    'has_custodian_debit': True,
                    'has_custodian_credit': True,
                    'has_partner_debit': True,
                    'has_partner_credit': True,
                    'has_symbol': False,
                    'has_dot': False,
                }
            )

            return Operation.objects.get_or_create(
                operation_code=operation_code,
                defaults={
                    'operation_name': operation_code,
                    'debit': default_accounting,
                    'credit': default_accounting,
                }
            )[0]

    @classmethod
    def calculate_incremental_accruals(cls, rows: list[dict]) -> list[dict]:
        prev_total = 0
        for row in rows:
            accrual_total = row.get('accruals_total', 0.0)
            row['accrual'] = accrual_total - prev_total
            prev_total = accrual_total
        return rows

    @classmethod
    def apply_coupon_reversal(cls, rows: list[dict]) -> list[dict]:
        cumulative = 0
        for row in rows:
            if row.get('operation') == 'BOND_COUPON_RECEIVED_BROKER':
                row['accrual'] = 0
                row['accrual_valuta'] = -cumulative
            else:
                row['accrual_valuta'] = row.get('accrual', 0.0)
                cumulative += row['accrual_valuta']
        return rows

    @classmethod
    def compute_ron_conversion(cls, rows: list[dict]) -> list[dict]:
        for row in rows:
            row['accrual_ron'] = round(row.get('accrual_valuta', 0.0) * row.get('bnr', 1.0), 2)
        return rows

    @classmethod
    def cumulate_totals(cls, rows: list[dict]) -> list[dict]:
        total_valuta = 0
        total_ron = 0
        for row in rows:
            total_valuta += row.get('accrual_valuta', 0.0)
            total_ron += row.get('accrual_ron', 0.0)
            row['total_accrual_valuta'] = total_valuta
            row['total_accrual_ron'] = total_ron
        return rows

    @classmethod
    def revalue_accruals(cls, rows: list[dict]) -> list[dict]:
        for row in rows:
            bnr = row.get('bnr', 1.0)
            bnr_eom = row.get('bnr_eom', bnr)
            revalue_rate = bnr_eom if row.get('revalue') else bnr
            row['revalue_total'] = round(row.get('total_accrual_valuta', 0.0) * revalue_rate, 2)
        return rows

    @classmethod
    def calculate_fx_diff(cls, rows: list[dict]) -> list[dict]:
        prev_total_fx = 0
        for row in rows:
            total_fx = row.get('revalue_total', 0.0) - row.get('total_accrual_ron', 0.0)
            row['total_fx_diff'] = total_fx
            row['fx_diff'] = round(total_fx - prev_total_fx, 2)
            prev_total_fx = total_fx
        return rows

    @classmethod
    def round_fields(cls, rows: list[dict]) -> list[dict]:
        round_cols = [
            'value', 'accruals_total', 'accrual', 'accrual_valuta', 'accrual_ron',
            'total_accrual_valuta', 'total_accrual_ron', 'revalue_total', 'total_fx_diff', 'fx_diff'
        ]
        for row in rows:
            for col in round_cols:
                if col in row and isinstance(row[col], (float, int)):
                    row[col] = round(row[col], 2)
        return rows


    @classmethod
    def enrich_with_bnr(cls, rows: list[dict], bnr_qs, bnr_eom_qs) -> list[dict]:
        def build_sorted_rate_dict(queryset):
            rate_dict = defaultdict(list)
            for obj in queryset:
                if not isinstance(obj, dict):
                    currency = obj.currency_code.currency_code
                    rate_dict[currency].append((obj.date, obj.value))
                else:
                    currency = obj['currency_code']
                    rate_dict[currency].append((obj['date'], obj['value_exact']))
                
            for k in rate_dict:
                rate_dict[k].sort()
            return rate_dict

        bnr_by_currency = build_sorted_rate_dict(bnr_qs)
        bnr_eom_by_currency = build_sorted_rate_dict(bnr_eom_qs)

        def get_latest_rate(date_list, rate_list, target_date, allow_exact):
            dates = [d for d, _ in date_list]
            i = bisect_right(dates, target_date)
            if allow_exact:
                i = i - 1
            else:
                i = i - 1 if i > 0 and dates[i-1] != target_date else i - 2
            if i >= 0:
                return rate_list[i]
            return None

        for row in rows:
            currency = row.get('currency')
            currency_code = currency.currency_code if hasattr(currency, 'currency_code') else currency
            row_date = row['date']

            daily_rates = bnr_by_currency.get(currency_code, [])
            if daily_rates:
                rate = get_latest_rate(daily_rates, [r for _, r in daily_rates], row_date, allow_exact=False)
                row['bnr'] = rate

            eom_rates = bnr_eom_by_currency.get(currency_code, [])
            if eom_rates:
                rate = get_latest_rate(eom_rates, [r for _, r in eom_rates], row_date, allow_exact=True)
                row['bnr_eom'] = rate

        return rows


    @classmethod
    def compute_incremental_accruals(cls, full_group: list[dict]) -> list[dict]:
        filtered = [
            row for row in full_group
            if row.get('operation') in ['INTEREST_ACCRUAL_BOND', 'BOND_COUPON_RECEIVED_BROKER']
        ]

        filtered.sort(key=lambda x: (x['date'], x.get('priority', 0)))

        prev_total = 0
        accrual_by_key = {}  # (date, operation) -> incremental accrual

        for row in filtered:
            accrual_total = row.get('accruals_total', 0.0)
            accrual_settled = row.get('accrual_settled', 0.0)
            incremental = accrual_total - prev_total + accrual_settled
            prev_total = accrual_total

            if row.get('operation') == 'INTEREST_ACCRUAL_BOND':
                accrual_by_key[(row['date'], row['operation'])] = incremental

        # 4. Apply incremental value to full_group rows
        for row in full_group:
            if row.get('operation') == 'INTEREST_ACCRUAL_BOND':
                key = (row['date'], row['operation'])
                row['accrual_incremental'] = accrual_by_key.get(key, 0.0)
                row['value'] = row['accrual_incremental']

        return full_group


    @classmethod
    def apply_coupon_settled(cls, full_group: list[dict]) -> list[dict]:
        coupon_payments_dict = {
            row['date']: row.get('value', 0.0)
            for row in full_group
            if row.get('operation') == 'BOND_COUPON_RECEIVED_BROKER'
        }

        for row in full_group:
            if row.get('operation') == 'INTEREST_ACCRUAL_BOND':
                row['coupon_settled'] = coupon_payments_dict.get(row['date'], 0.0)
            else:
                row['coupon_settled'] = 0.0

        for row in full_group:
            if row.get('coupon_settled', 0) > 0:
                row['accruals_total'] = row['coupon_settled']

        return full_group

    @classmethod
    def map_accrual_settled(cls, full_group: list[dict]) -> list[dict]:
        op_updated = 'INTEREST_ACCRUAL_BOND'
        ops_settled = ['BOND_INTEREST_PAID_BROKER', 'BOND_INTEREST_RECEIVED_BROKER']

        settled_sum_by_date = defaultdict(float)
        for row in full_group:
            if row.get('operation') in ops_settled:
                settled_sum_by_date[row['date']] += row.get('value', 0.0)

        for row in full_group:
            if row.get('operation') == op_updated:
                row['accrual_settled'] = settled_sum_by_date.get(row['date'], 0.0)
            else:
                row['accrual_settled'] = 0.0

        full_group.sort(key=lambda r: (r['date'], r.get('priority', 0)))

        return full_group

    @classmethod
    def calculate_accruals_for_group(cls, journal_group: list[dict], bond_info: Instrument) -> list[dict]:
        if not journal_group:
            return []

        for row in journal_group:
            date = row['date']
            trade_date = ql.Date(date.day, date.month, date.year)

            # Get applicable calendar from mapping
            calendar = cls.CALENDAR_MAP.get(bond_info.calendar, cls.CALENDAR_MAP['TARGET'])
            
            # Get payment_convention from mapping
            payment_convention = cls.PAYMENT_CONVENTION.get(bond_info.convention, cls.PAYMENT_CONVENTION['ACT/ACT'])

            # Create schedule
            schedule_coupon = cls.get_coupon_schedule(bond_info)
            coupon_dates = list(schedule_coupon.dates())
            # print(coupon_dates)

            settlement_days = 2

            # Adjust for reduction of settlement for USD after May 28, 2024
            new_settlement_cutoff_date = ql.Date(28, 5, 2024)
            if (bond_info.currency == 'USD') and (trade_date>new_settlement_cutoff_date):
                settlement_days = 1

            # Ad-hoc correction, not really correct
            if bond_info.calendar == 'Settlement':
                settlement_days = 1


            target_settlement_date = calendar.advance(
                trade_date,
                ql.Period(settlement_days, ql.Days),
                ql.Following
            )

            if target_settlement_date in coupon_dates:
                # If target settlement date is a coupon date, trade_date as settlemnt date
                settlement_date = trade_date
            else:
                settlement_date = target_settlement_date

            # Set evaluation date to settlement date
            ql.Settings.instance().evaluationDate = settlement_date

            # Create fixed rate bond
            coupon_rate = bond_info.interest / 100.0
            coupon_rates = [coupon_rate]
            
            bond = ql.FixedRateBond(
                settlement_days,  # 0 days for coupon dates
                100,                 # Face amount
                schedule_coupon,
                coupon_rates,
                payment_convention
                # ql.Thirty360(ql.Thirty360.ISMA)
                # ql.Thirty360(ql.Thirty360.European)
                # ql.ActualActual(ql.ActualActual.ISMA)
                )

            # Calculate accrued interest using settlement date
            accrued_percentage = bond.accruedAmount(settlement_date)

            row['accruals_total'] = (accrued_percentage / 100.0) * row['quantity']
        return journal_group
    
    @classmethod
    def adjust_accruals(cls, journal_group: list[dict], bond_info: Instrument,) -> list[dict]:
        if not journal_group:
            return []

        start_date = min(row['date'] for row in journal_group)
        end_date = min(datetime.today().date(), bond_info.maturity)
        accrual_days = [
            (start_date.replace(day=1) + relativedelta(months=i+1) - relativedelta(days=1)) 
            for i in range((end_date.year - start_date.year) * 12 + end_date.month - start_date.month + 1)
        ]

        all_dates_in_group = sorted(set(row['date'] for row in journal_group))
        other_accrual_dates = sorted(set(all_dates_in_group) - set(accrual_days))

        symbol = bond_info.symbol
        INDEX_COLS = ['ubo', 'custodian', 'partner', 'date', 'symbol', 'currency', 'operation', 'account', 'instrument']

        monthly_accruals = [{
            'operation': 'INTEREST_ACCRUAL_BOND',
            'date': d,
            'details': f'Monthly accrual {symbol}',
            'quantity': 0,
            'priority': 4
        } for d in accrual_days]

        interim_accruals = [{
            'operation': 'INTEREST_ACCRUAL_BOND',
            'date': d,
            'details': f'Interm. accrual trade date {symbol}',
            'quantity': 0,
            'priority': 3
        } for d in other_accrual_dates]

        logger.debug(f"Generated accruals for {symbol}: {len(monthly_accruals)} monthly + {len(interim_accruals)} interim")
        logger.debug(f"Monthly accrual dates: {[d for d in accrual_days]}")
        logger.debug(f"Interim accrual dates: {[d for d in other_accrual_dates]}")

        full_group = copy.deepcopy(journal_group)
        for row in chain(monthly_accruals, interim_accruals):
            full_group.append(row)

        full_group.sort(key=lambda x: (x['date'], x.get('priority', 0)))

        last_values = {}
        for row in full_group:
            for col in INDEX_COLS:
                if col in row and row[col] is not None:
                    last_values[col] = row[col]
                else:
                    row[col] = last_values.get(col)

        quantity_total = 0
        for row in full_group:
            quantity_total += row.get('quantity', 0) or 0
            row['quantity_total'] = quantity_total

        return full_group
    
    @classmethod
    def set_priority(cls, grouped_bonds: list[dict]) -> list[dict]:
        for entry in grouped_bonds:
            if entry.get("operation") == "BOND_COUPON_RECEIVED_BROKER":
                entry["priority"] = 10
            else:
                entry["priority"] = 1
        return grouped_bonds
    
    @classmethod
    def correct_bond_coupon_received_dates(cls, grouped_bonds: list[dict], coupon_dates: list[date]) -> list[dict]:
        corrected_bonds = []

        for entry in grouped_bonds:
            if entry.get("operation") == "BOND_COUPON_RECEIVED_BROKER":
                original_date = entry["date"]
                valid_coupon_dates = [
                    cd for cd in coupon_dates 
                    if cd <= original_date and (original_date - cd).days <= 5
                ]

                if valid_coupon_dates:
                    closest_coupon_date = max(valid_coupon_dates)
                    entry["date"] = closest_coupon_date  # update date
            corrected_bonds.append(entry)

        return corrected_bonds
    
    @classmethod
    def group_bounds_by_symbols_and_date(cls, bond_journal_qs: QuerySet[Journal]) -> list[dict]:
        qs = (
            bond_journal_qs.annotate(
                bond_quantity=F('quantity') * F('instrument__face_value')
            ).values(
                'id',
                'ubo__ubo_code',
                'custodian__custodian_code',
                'partner__partner_code',
                'operation__operation_code',
                'instrument__symbol',
                'instrument__currency__currency_code',
                'account__account_code',
                'date',
                'value',
                'bnr',
                'value_ron',
                'details',
            )
            .annotate(
                total_value=Sum('value'),
                bond_quantity=Sum('bond_quantity'),
                detail_list=ArrayAgg('details', distinct=True),
            )
        )
        qs = qs.annotate(
            details_string=ArrayToString('detail_list')
        )
        qs = qs.order_by('instrument__symbol', 'date')
        
        rename_map = {
            'instrument__currency__currency_code': 'currency',
            'operation__operation_code': 'operation',
            'instrument__symbol': 'symbol',
            'partner__partner_code': 'partner',
            'custodian__custodian_code': 'custodian',
            'account__account_code': 'account',
            'ubo__ubo_code': 'ubo',
            'bond_quantity': 'quantity',
        }
        qs_list = list(qs)
        renamed_qs = [
            {rename_map.get(k, k): v for k, v in row.items()}
            for row in qs_list
        ]
        return renamed_qs
    
    @classmethod
    def get_bonds_data(cls):
        bond_journal_qs = cls.get_bond_journals()
        if not bond_journal_qs.exists():
            logger.info("No bond operations found")
            return

        bond_info_qs = cls.get_bond_instruments()
        if not bond_info_qs.exists():
            logger.info("No bond instruments found")
            return

        bnr_qs = cls.get_bnr_data()
        bnr_eom_qs = cls.get_eom_rates()

        return bond_journal_qs, bnr_qs, bnr_eom_qs, bond_info_qs
    
    
    @classmethod
    def get_bond_journals(cls,) -> QuerySet[Journal]:
        return Journal.objects.filter(
            operation__operation_code__contains='BOND'
            ).exclude(
                operation__operation_code__in=[
                    'INTEREST_ACCRUAL_BOND', 'BOND_ACCRUAL_REVERSAL',
                    'FX_DIF_ACCRUAL_MINUS', 'FX_DIF_ACCRUAL_PLUS',
                ]
            ).select_related(
                'operation',
                'instrument',
                'instrument__currency',
            )

    @classmethod
    def get_bnr_data(cls) -> QuerySet[Bnr]:
        return Bnr.objects.filter(
            currency_code__currency_code__in=['EUR', 'USD', 'MXN']
            ).exclude(value_exact=None).select_related(
                'currency_code'
            )

    @classmethod
    def get_eom_rates(cls) -> list[dict]:
        query = """
            SELECT DISTINCT ON (bnr.currency_code_id, DATE_TRUNC('month', bnr.date))
                bnr.id,
                currency.currency_code,
                bnr.date,
                bnr.value_exact
            FROM port_bnr bnr
            JOIN port_currency currency ON bnr.currency_code_id = currency.id
            WHERE bnr.value_exact IS NOT NULL
            AND currency.currency_code IN ('EUR', 'USD', 'MXN')
            ORDER BY bnr.currency_code_id, DATE_TRUNC('month', bnr.date), bnr.date DESC;
        """

        with connection.cursor() as cursor:
            cursor.execute(query)
            columns = [col[0] for col in cursor.description]
            results = [dict(zip(columns, row)) for row in cursor.fetchall()]
        return results

    @classmethod
    def get_bond_instruments(cls) -> QuerySet[Instrument]:
        return Instrument.objects.filter(
            type='BOND'
            ).select_related(
                'currency',
                'custodian'
            )

    @classmethod
    def get_coupon_schedule(cls, bond_info: Instrument):
        """Get a list of all coupon dates for a bond"""
        calendar = cls.CALENDAR_MAP.get(bond_info.calendar, cls.CALENDAR_MAP['TARGET'])
        
        frequency = ql.Annual
        if bond_info.bond_coupon_count == 2:
            frequency = ql.Semiannual

        issue_date = ql.Date(bond_info.bond_issue.day, 
                    bond_info.bond_issue.month,
                    bond_info.bond_issue.year)
        maturity_date = ql.Date(bond_info.maturity.day,
                        bond_info.maturity.month, 
                        bond_info.maturity.year)

        schedule_coupon = ql.Schedule(
            issue_date,
            maturity_date,
            ql.Period(frequency),
            calendar,
            ql.Unadjusted,    # Payment convention
            ql.Unadjusted,    # Termination convention
            ql.DateGeneration.Backward,   # Date generation
            True,
            )

        return schedule_coupon

    @classmethod
    def _preload_bnr_cache(cls, bnr_qs: QuerySet, bnr_eom_qs: list):
        """Pre-load BNR rates into cache for performance"""
        logger.info("Pre-loading BNR rates cache...")

        # Load regular BNR rates (Django model objects)
        for bnr in bnr_qs:
            currency_code = bnr.currency_code.currency_code
            cache_key = (currency_code, bnr.date)
            cls._bnr_cache[cache_key] = bnr.value_exact

        # Load end-of-month BNR rates (dictionaries from raw SQL)
        for bnr_dict in bnr_eom_qs:
            currency_code = bnr_dict['currency_code']
            cache_key = (f"{currency_code}_EOM", bnr_dict['date'])
            cls._bnr_cache[cache_key] = bnr_dict['value_exact']

        logger.info(f"Loaded {len(cls._bnr_cache)} BNR rates into cache")

    @classmethod
    def _get_cached_bnr_rate(cls, currency_code: str, target_date: date, is_eom: bool = False) -> float:
        """Get BNR rate from cache"""
        if currency_code == 'RON':
            return 1.0

        cache_key_prefix = f"{currency_code}_EOM" if is_eom else currency_code
        cache_key = (cache_key_prefix, target_date)

        if cache_key in cls._bnr_cache:
            return cls._bnr_cache[cache_key]

        # Find closest previous date in cache
        available_keys = [k for k in cls._bnr_cache.keys()
                         if k[0] == cache_key_prefix and k[1] <= target_date]

        if available_keys:
            closest_key = max(available_keys, key=lambda x: x[1])
            rate = cls._bnr_cache[closest_key]
            # Cache the result for this specific date
            cls._bnr_cache[cache_key] = rate
            return rate

        return 1.0  # Fallback

    @classmethod
    def _process_bond_symbol_optimized(cls, symbol_bonds: QuerySet, instrument_symbol: Instrument,
                                     bnr_qs: QuerySet, bnr_eom_qs: QuerySet) -> List[Dict]:
        """Optimized processing of a single bond symbol"""
        grouped_bonds = cls.group_bounds_by_symbols_and_date(symbol_bonds)
        coupon_dates = cls.get_coupon_schedule(instrument_symbol)
        coupon_dates = [date(d.year(), d.month(), d.dayOfMonth()) for d in coupon_dates]

        # Apply all transformations
        grouped_bonds = cls.correct_bond_coupon_received_dates(grouped_bonds, coupon_dates)
        grouped_bonds = cls.set_priority(grouped_bonds)
        grouped_bonds = cls.adjust_accruals(grouped_bonds, instrument_symbol)
        grouped_bonds = cls.calculate_accruals_for_group_optimized(grouped_bonds, instrument_symbol)
        grouped_bonds = cls.map_accrual_settled(grouped_bonds)
        grouped_bonds = cls.apply_coupon_settled(grouped_bonds)
        grouped_bonds = cls.compute_incremental_accruals(grouped_bonds)
        grouped_bonds = cls.enrich_with_bnr_optimized(grouped_bonds, bnr_qs, bnr_eom_qs)
        grouped_bonds = cls.calculate_incremental_accruals(grouped_bonds)
        grouped_bonds = cls.apply_coupon_reversal(grouped_bonds)
        grouped_bonds = cls.compute_ron_conversion(grouped_bonds)
        grouped_bonds = cls.cumulate_totals(grouped_bonds)
        grouped_bonds = cls.revalue_accruals(grouped_bonds)
        grouped_bonds = cls.calculate_fx_diff(grouped_bonds)
        grouped_bonds = cls.round_fields(grouped_bonds)

        # Create FX difference operations (critical step from original)
        fx_operations = cls.create_fx_diff_operations(grouped_bonds)

        # Merge FX operations into main data (like original line 302)
        grouped_bonds.extend(fx_operations)

        # Sort by date and priority (like original line 304)
        grouped_bonds.sort(key=lambda x: (x['date'], x.get('priority', 0)))

        return grouped_bonds

    @classmethod
    def create_fx_diff_operations(cls, grouped_bonds: List[Dict]) -> List[Dict]:
        """Create FX difference operations similar to original lines 285-296"""
        fx_operations = []

        for row in grouped_bonds:
            fx_diff = row.get('fx_diff', 0)
            if fx_diff != 0:
                # Determine operation type based on fx_diff sign
                fx_operation_code = 'FX_DIF_ACCRUAL_PLUS' if fx_diff > 0 else 'FX_DIF_ACCRUAL_MINUS'

                # Create FX operation entry (similar to original lines 285-296)
                fx_operation = {
                    'ubo': row['ubo'],
                    'custodian': row['custodian'],
                    'partner': row['partner'],
                    'date': row['date'],
                    'symbol': row['symbol'],
                    'currency': 'RON',  # FX operations are always in RON
                    'operation': fx_operation_code,
                    'account': f"{row['custodian']}_RON",  # RON account
                    'instrument': f"{row['custodian']}_{row['symbol']}",  # RON instrument
                    'value': abs(fx_diff),  # Store absolute value
                    'quantity': 0,
                    'details': f"FxDifAcc {row.get('details', '')}",
                    'priority': 5,  # Set priority for sorting
                    'fx_diff': 0,  # FX operations don't have further FX differences
                    'accrual_incremental': 0,
                }

                # Copy other fields that might be needed
                for field in ['accruals_total', 'accrual_settled', 'coupon_settled',
                             'total_accrual_valuta', 'total_accrual_ron', 'revalue_total',
                             'total_fx_diff', 'bnr', 'bnr_eom']:
                    fx_operation[field] = row.get(field, 0)

                fx_operations.append(fx_operation)

        return fx_operations

    @classmethod
    def calculate_accruals_for_group_optimized(cls, journal_group: List[Dict], bond_info: Instrument) -> List[Dict]:
        """Optimized version using cached QuantLib objects"""
        if not journal_group:
            return []

        # Create or get cached bond object
        bond_cache_key = f"{bond_info.symbol}_{bond_info.interest}_{bond_info.maturity}"

        if bond_cache_key not in cls._bond_cache:
            # Create QuantLib bond object and cache it
            cls._bond_cache[bond_cache_key] = cls._create_quantlib_bond(bond_info)

        bond = cls._bond_cache[bond_cache_key]

        # Process each journal entry
        for row in journal_group:
            settlement_date = ql.Date(row['date'].day, row['date'].month, row['date'].year)

            # Set evaluation date to settlement date
            ql.Settings.instance().evaluationDate = settlement_date

            # Calculate accrued interest using cached bond
            accrued_percentage = bond.accruedAmount(settlement_date)
            row['accruals_total'] = (accrued_percentage / 100.0) * row['quantity']

        return journal_group

    @classmethod
    def _create_quantlib_bond(cls, bond_info: Instrument) -> ql.FixedRateBond:
        """Create and cache QuantLib bond object"""
        # Create schedule (cache this too if needed)
        schedule_key = (bond_info.symbol, bond_info.bond_issue, bond_info.maturity, bond_info.bond_coupon_count)

        if schedule_key not in cls._schedule_cache:
            schedule_coupon = cls.get_coupon_schedule(bond_info)
            cls._schedule_cache[schedule_key] = schedule_coupon
        else:
            schedule_coupon = cls._schedule_cache[schedule_key]

        # Bond parameters
        settlement_days = 0
        payment_convention = cls.PAYMENT_CONVENTION.get(bond_info.convention, cls.PAYMENT_CONVENTION['30/360'])
        coupon_rate = bond_info.interest / 100.0
        coupon_rates = [coupon_rate]

        # Create bond
        bond = ql.FixedRateBond(
            settlement_days,
            100,  # Face amount
            schedule_coupon,
            coupon_rates,
            payment_convention
        )

        return bond

    @classmethod
    def enrich_with_bnr_optimized(cls, grouped_bonds: List[Dict], bnr_qs: QuerySet, bnr_eom_qs: list) -> List[Dict]:
        """Optimized BNR enrichment using cache"""
        for row in grouped_bonds:
            currency_code = row.get('currency', 'RON')
            target_date = row['date']

            # Get regular BNR rate from cache
            row['bnr'] = cls._get_cached_bnr_rate(currency_code, target_date, is_eom=False)

            # Get end-of-month BNR rate from cache
            row['bnr_eom'] = cls._get_cached_bnr_rate(currency_code, target_date, is_eom=True)

        return grouped_bonds

    @classmethod
    def _prepare_bond_accrual_data(cls, row: Dict, instrument: Instrument) -> Dict:
        """Prepare bond accrual data for bulk storage"""
        try:
            return {
                'ubo_id': 1,  # Default UBO
                'custodian_id': instrument.custodian.id,
                'partner_id': 1,  # Default partner
                'account_id': 1,  # Default account
                'instrument_id': instrument.id,
                'operation_id': 1,  # Default operation
                'currency_id': instrument.currency.id,
                'date': row['date'],
                'details': f"{instrument.symbol} ACCRUAL",
                'priority': row.get('priority', 1),
                'quantity': row.get('quantity', 0),
                'quantity_total': row.get('quantity_total', 0),
                'value': row.get('value', 0),
                'accruals_total': row.get('accruals_total', 0),
                'accrual': row.get('accrual', 0),
                'accrual_valuta': row.get('accrual_valuta', 0),
                'accrual_ron': row.get('accrual_ron', 0),
                'total_accrual_valuta': row.get('total_accrual_valuta', 0),
                'total_accrual_ron': row.get('total_accrual_ron', 0),
                'revalue_total': row.get('revalue_total', 0),
                'total_fx_diff': row.get('total_fx_diff', 0),
                'fx_diff': row.get('fx_diff', 0),
                'bnr': row.get('bnr'),
                'bnr_eom': row.get('bnr_eom'),
                'coupon_settled': row.get('coupon_settled', 0),
                'accrual_settled': row.get('accrual_settled', 0),
                'accrual_incremental': row.get('accrual_incremental', 0),
            }
        except KeyError as e:
            logger.warning(f"Missing key in bond accrual data: {e}")
            return None
        except Exception as e:
            logger.error(f"Error preparing bond accrual data: {e}")
            return None

    @classmethod
    def _bulk_store_bond_accruals(cls, accrual_data_list: List[Dict]) -> int:
        """Bulk store bond accruals for better performance"""
        if not accrual_data_list:
            return 0

        created_count = 0

        try:
            with transaction.atomic():
                for data in accrual_data_list:
                    try:
                        # Use update_or_create to handle potential duplicates
                        bond_accrual, created = BondAccrual.objects.update_or_create(
                            ubo_id=data['ubo_id'],
                            custodian_id=data['custodian_id'],
                            partner_id=data['partner_id'],
                            account_id=data['account_id'],
                            instrument_id=data['instrument_id'],
                            operation_id=data['operation_id'],
                            currency_id=data['currency_id'],
                            date=data['date'],
                            defaults={
                                'details': data['details'],
                                'priority': data['priority'],
                                'quantity': data['quantity'],
                                'quantity_total': data['quantity_total'],
                                'value': data['value'],
                                'accruals_total': data['accruals_total'],
                                'accrual': data['accrual'],
                                'accrual_valuta': data['accrual_valuta'],
                                'accrual_ron': data['accrual_ron'],
                                'total_accrual_valuta': data['total_accrual_valuta'],
                                'total_accrual_ron': data['total_accrual_ron'],
                                'revalue_total': data['revalue_total'],
                                'total_fx_diff': data['total_fx_diff'],
                                'fx_diff': data['fx_diff'],
                                'bnr': data['bnr'],
                                'bnr_eom': data['bnr_eom'],
                                'coupon_settled': data['coupon_settled'],
                                'accrual_settled': data['accrual_settled'],
                                'accrual_incremental': data['accrual_incremental'],
                            }
                        )

                        if created:
                            created_count += 1

                    except Exception as e:
                        logger.error(f"Error creating bond accrual for instrument {data.get('instrument_id')}: {e}")

                logger.info(f"Created/updated {created_count} bond accruals")
                return created_count

        except Exception as e:
            logger.error(f"Error in bulk bond accrual creation: {e}", exc_info=True)
            return 0
